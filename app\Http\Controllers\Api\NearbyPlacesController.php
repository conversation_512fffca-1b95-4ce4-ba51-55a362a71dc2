<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\NearbyPlacesRequest;
use App\Http\Resources\PlaceResource;
use App\Models\Place;
use App\Models\SystemSetting;
use Illuminate\Http\Resources\Json\ResourceCollection;

class NearbyPlacesController extends Controller
{
    /**
     * Get places near the user's location based on distance
     *
     * @param NearbyPlacesRequest $request
     * @return ResourceCollection
     */
    public function __invoke(NearbyPlacesRequest $request): ResourceCollection
    {
        // Get the distance from system settings (default 5km)
        $distance = SystemSetting::getValue(SystemSetting::KEY_NEARBY_PLACES_DISTANCE, 5);

        // Calculate the distance using the Haversine formula
        // This formula calculates the great-circle distance between two points on a sphere
        // given their longitudes and latitudes
        $haversineFormula = '(
            6371 * acos(
                LEAST(1, GREATEST(-1,
                    cos(radians(?)) *
                    cos(radians(latitude)) *
                    cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(latitude))
                ))
            )
        )';

        $query = Place::query()
            ->active()
            ->select('*')
            ->selectRaw("$haversineFormula AS distance", [
                $request->get('lat'),
                $request->get('lng'),
                $request->get('lat')
            ])
            ->whereRaw("$haversineFormula <= ?", [
                $request->get('lat'),
                $request->get('lng'),
                $request->get('lat'),
                $distance
            ]);

        // Filter by agent IDs if provided
        if ($request->has('agent_ids') && !empty($request->get('agent_ids'))) {
            $agentIds = explode(',', $request->get('agent_ids'));
            $agentIds = array_map('intval', $agentIds); // Convert to integers
            $agentIds = array_filter($agentIds); // Remove any invalid values

            if (!empty($agentIds)) {
                $query->whereIn('agent_id', $agentIds);
            }
        }

        $places = $query->orderByRaw("$haversineFormula ASC", [
                $request->get('lat'),
                $request->get('lng'),
                $request->get('lat')
            ])
            ->get();

        return PlaceResource::collection($places);
    }
}
