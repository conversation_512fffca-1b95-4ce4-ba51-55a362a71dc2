<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserRecommendationUsage extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_recommendation_usage';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'count',
        'last_reset_at',
        'next_reset_at',
        'has_been_notified_of_renewal',
        'last_notification_shown_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'count' => 'integer',
        'last_reset_at' => 'datetime',
        'next_reset_at' => 'datetime',
        'has_been_notified_of_renewal' => 'boolean',
        'last_notification_shown_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns the usage record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Increment the usage count for a user
     *
     * @param User $user
     * @return UserRecommendationUsage
     */
    public static function incrementUsage(User $user): UserRecommendationUsage
    {
        return self::incrementUsageByCount($user, 1);
    }

    /**
     * Increment the usage count for a user by a specific count
     *
     * @param User $user
     * @param int $count The number to increment by
     * @return UserRecommendationUsage
     */
    public static function incrementUsageByCount(User $user, int $count): UserRecommendationUsage
    {
        $usage = self::firstOrNew(['user_id' => $user->id]);

        // If this is a new record, set the reset dates
        if (!$usage->exists) {
            $renewalDays = SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                7
            );

            $usage->last_reset_at = now();
            $usage->next_reset_at = now()->addDays($renewalDays);
            $usage->count = 0;
        }

        // Check if we need to reset the count
        if ($usage->next_reset_at && $usage->next_reset_at->isPast()) {
            $renewalDays = SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                7
            );

            $usage->last_reset_at = now();
            $usage->next_reset_at = now()->addDays($renewalDays);
            $usage->count = 0;
            $usage->has_been_notified_of_renewal = false;
        }

        // Increment the count by the specified amount
        $usage->count += $count;
        $usage->save();

        return $usage;
    }

    /**
     * Check if a user has reached their recommendation limit
     *
     * @param User $user
     * @return bool
     */
    public static function hasReachedLimit(User $user): bool
    {
        $usage = self::where('user_id', $user->id)->first();

        // If no usage record exists, user hasn't reached limit
        if (!$usage) {
            return false;
        }

        // Check if we need to reset the count
        if ($usage->next_reset_at && $usage->next_reset_at->isPast()) {
            $renewalDays = SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                7
            );

            $usage->last_reset_at = now();
            $usage->next_reset_at = now()->addDays($renewalDays);
            $usage->count = 0;
            $usage->has_been_notified_of_renewal = false;
            $usage->save();

            return false;
        }

        // Get the limit from system settings
        $limit = SystemSetting::getValue(
            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
            10
        );

        return $usage->count >= $limit;
    }

    /**
     * Get the remaining recommendations for a user
     *
     * @param User $user
     * @return int
     */
    public static function getRemainingCount(User $user): int
    {
        $usage = self::where('user_id', $user->id)->first();

        // If no usage record exists, user has full limit
        if (!$usage) {
            return SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                10
            );
        }

        // Check if we need to reset the count
        if ($usage->next_reset_at && $usage->next_reset_at->isPast()) {
            $renewalDays = SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                7
            );

            $usage->last_reset_at = now();
            $usage->next_reset_at = now()->addDays($renewalDays);
            $usage->count = 0;
            $usage->has_been_notified_of_renewal = false;
            $usage->save();

            return SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                10
            );
        }

        // Calculate remaining count
        $limit = SystemSetting::getValue(
            SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
            10
        );

        return max(0, $limit - $usage->count);
    }

    /**
     * Check if a user should be shown a quota renewal notification
     *
     * @param User $user
     * @return bool
     */
    public static function shouldShowRenewalNotification(User $user): bool
    {
        // Don't show notifications for anonymous users or premium users
        if ($user->is_anonymous || $user->hasActiveSubscription()) {
            return false;
        }

        $usage = self::where('user_id', $user->id)->first();

        // If no usage record exists, no notification needed
        if (!$usage) {
            return false;
        }

        // If user has never been notified and has a reset date, show notification
        if (!$usage->has_been_notified_of_renewal && $usage->last_reset_at) {
            return true;
        }

        // If user was notified before, check if there was a renewal since last notification
        if ($usage->has_been_notified_of_renewal &&
            $usage->last_notification_shown_at &&
            $usage->last_reset_at &&
            $usage->last_reset_at->isAfter($usage->last_notification_shown_at)) {
            return true;
        }

        return false;
    }

    /**
     * Mark that the user has been notified of quota renewal
     *
     * @param User $user
     * @return void
     */
    public static function markAsNotified(User $user): void
    {
        $usage = self::where('user_id', $user->id)->first();

        if ($usage) {
            $usage->has_been_notified_of_renewal = true;
            $usage->last_notification_shown_at = now();
            $usage->save();
        }
    }

    /**
     * Reset notification status when quota is renewed
     * This should be called when the scheduled task resets quotas
     *
     * @param User $user
     * @return void
     */
    public static function resetNotificationStatus(User $user): void
    {
        $usage = self::where('user_id', $user->id)->first();

        if ($usage) {
            $usage->has_been_notified_of_renewal = false;
            $usage->save();
        }
    }
}
