<?php

namespace App\Filament\Resources;

use App\Filament\Actions\IsAdminAction;
use App\Filament\Resources\FilterAccessControlResource\Pages;
use App\Models\FilterAccessControl;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;

class FilterAccessControlResource extends Resource
{
    protected static ?string $model = FilterAccessControl::class;

    protected static ?string $navigationIcon = 'heroicon-o-funnel';

    protected static ?string $navigationLabel = 'Filter Access Control';

    protected static ?string $modelLabel = 'Filter Access Control';

    protected static ?string $pluralModelLabel = 'Filter Access Control';

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationGroup = 'App Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Filter Access Control Settings')
                    ->description('Control which filters require premium subscription')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('working_hours_locked')
                                    ->label('Working Hours Filter')
                                    ->helperText('Lock "Open now" filter for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('rating_locked')
                                    ->label('Rating Filter')
                                    ->helperText('Lock rating filters (3.5+, 4+, 4.5+) for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('family_friendly_locked')
                                    ->label('Family Friendly Filter')
                                    ->helperText('Lock family friendly filter for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('budget_locked')
                                    ->label('Budget Filter')
                                    ->helperText('Lock budget filters ($, $$, $$$) for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('wheelchair_accessible_locked')
                                    ->label('Wheelchair Accessible Filter')
                                    ->helperText('Lock wheelchair accessible filter for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('pet_friendly_locked')
                                    ->label('Pet Friendly Filter')
                                    ->helperText('Lock pet friendly filter for premium users only')
                                    ->default(false),

                                Forms\Components\Toggle::make('sort_by_distance_locked')
                                    ->label('Sort by Distance')
                                    ->helperText('Lock distance sorting for premium users only')
                                    ->default(false),
                            ]),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Filter Status Overview')
                    ->description('Current status of all filters')
                    ->schema([
                        Forms\Components\Placeholder::make('locked_filters_count')
                            ->label('Locked Filters Count')
                            ->content(function ($record) {
                                if (!$record) return '0';
                                return $record->getLockedFilters()->count();
                            }),

                        Forms\Components\Placeholder::make('unlocked_filters_count')
                            ->label('Unlocked Filters Count')
                            ->content(function ($record) {
                                if (!$record) return '7';
                                return $record->getUnlockedFilters()->count();
                            }),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\IconColumn::make('working_hours_locked')
                    ->label('Working Hours')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('rating_locked')
                    ->label('Rating')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('family_friendly_locked')
                    ->label('Family Friendly')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('budget_locked')
                    ->label('Budget')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('wheelchair_accessible_locked')
                    ->label('Wheelchair')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('pet_friendly_locked')
                    ->label('Pet Friendly')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\IconColumn::make('sort_by_distance_locked')
                    ->label('Distance Sort')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                
                Action::make('lockAll')
                    ->label('Lock All')
                    ->icon('heroicon-o-lock-closed')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalDescription('This will lock all filters for premium users only.')
                    ->action(function (FilterAccessControl $record) {
                        $record->update([
                            'working_hours_locked' => true,
                            'rating_locked' => true,
                            'family_friendly_locked' => true,
                            'budget_locked' => true,
                            'wheelchair_accessible_locked' => true,
                            'pet_friendly_locked' => true,
                            'sort_by_distance_locked' => true,
                        ]);

                        Notification::make()
                            ->success()
                            ->title('All Filters Locked')
                            ->body('All filters are now locked for premium users only.')
                            ->send();
                    }),

                Action::make('unlockAll')
                    ->label('Unlock All')
                    ->icon('heroicon-o-lock-open')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalDescription('This will unlock all filters for all users.')
                    ->action(function (FilterAccessControl $record) {
                        $record->update([
                            'working_hours_locked' => false,
                            'rating_locked' => false,
                            'family_friendly_locked' => false,
                            'budget_locked' => false,
                            'wheelchair_accessible_locked' => false,
                            'pet_friendly_locked' => false,
                            'sort_by_distance_locked' => false,
                        ]);

                        Notification::make()
                            ->success()
                            ->title('All Filters Unlocked')
                            ->body('All filters are now available for all users.')
                            ->send();
                    }),
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFilterAccessControls::route('/'),
            'create' => Pages\CreateFilterAccessControl::route('/create'),
            'edit' => Pages\EditFilterAccessControl::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return IsAdminAction::handle();
    }

    public static function canCreate(): bool
    {
        // Only allow creation if no record exists
        return IsAdminAction::handle() && FilterAccessControl::count() === 0;
    }
}
