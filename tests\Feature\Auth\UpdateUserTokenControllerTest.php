<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Enums\UserType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UpdateUserTokenControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user (required for some tests)
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);

        // Create a user for authentication
        $this->user = User::factory()->create([
            'is_first_login' => true,
        ]);
    }

    /**
     * Test that unauthenticated users cannot access the update token endpoint.
     */
    public function test_unauthenticated_user_cannot_access_update_token(): void
    {
        $response = $this->postJson('/api/users/update-user-token');

        $response->assertStatus(401);
    }

    /**
     * Test that authenticated users can update their token.
     */
    public function test_authenticated_user_can_update_token(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'email',
                    'is_anonymous',
                    'is_first_login',
                    'points',
                    'created_at',
                    'updated_at',
                ],
                'meta' => [
                    'token',
                ],
            ])
            ->assertJsonPath('data.id', $this->user->id)
            ->assertJsonPath('data.email', $this->user->email);

        // Verify that the token is provided
        $this->assertNotNull($response->json('meta.token'));
    }

    /**
     * Test that is_first_login is set to false after token update.
     */
    public function test_is_first_login_set_to_false_after_token_update(): void
    {
        // Ensure user starts with is_first_login = true
        $this->assertTrue($this->user->is_first_login);

        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonPath('data.is_first_login', false);

        // Verify in database
        $this->user->refresh();
        $this->assertFalse($this->user->is_first_login);
    }

    /**
     * Test that is_first_login remains false if already false.
     */
    public function test_is_first_login_remains_false_if_already_false(): void
    {
        // Set user's is_first_login to false
        $this->user->update(['is_first_login' => false]);

        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonPath('data.is_first_login', false);

        // Verify in database
        $this->user->refresh();
        $this->assertFalse($this->user->is_first_login);
    }

    /**
     * Test that user data is returned correctly.
     */
    public function test_returns_correct_user_data(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonPath('data.id', $this->user->id)
            ->assertJsonPath('data.name', $this->user->name)
            ->assertJsonPath('data.email', $this->user->email)
            ->assertJsonPath('data.is_anonymous', $this->user->is_anonymous);
    }

    /**
     * Test that token is refreshed (new token provided).
     */
    public function test_token_is_refreshed(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful();

        $token = $response->json('meta.token');
        $this->assertNotNull($token);
        $this->assertIsString($token);
        $this->assertNotEmpty($token);
    }

    /**
     * Test with anonymous user.
     */
    public function test_works_with_anonymous_user(): void
    {
        $anonymousUser = User::factory()->create([
            'is_anonymous' => true,
            'is_first_login' => true,
        ]);

        Sanctum::actingAs($anonymousUser);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonPath('data.id', $anonymousUser->id)
            ->assertJsonPath('data.is_anonymous', true)
            ->assertJsonPath('data.is_first_login', false);

        // Verify in database
        $anonymousUser->refresh();
        $this->assertFalse($anonymousUser->is_first_login);
    }

    /**
     * Test that the endpoint handles users with different user types.
     */
    public function test_works_with_different_user_types(): void
    {
        $adminUser = User::factory()->create([
            'user_type' => UserType::Admin,
            'is_first_login' => true,
        ]);

        Sanctum::actingAs($adminUser);

        $response = $this->postJson('/api/users/update-user-token');

        $response->assertSuccessful()
            ->assertJsonPath('data.id', $adminUser->id)
            ->assertJsonPath('data.is_first_login', false);

        // Verify in database
        $adminUser->refresh();
        $this->assertFalse($adminUser->is_first_login);
    }
}
