<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Filter Status Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            @php
                $record = \App\Models\FilterAccessControl::first();
                $lockedCount = $record ? $record->getLockedFilters()->count() : 0;
                $unlockedCount = $record ? $record->getUnlockedFilters()->count() : 7;
                $totalCount = 7;
                $premiumPercentage = $totalCount > 0 ? round(($lockedCount / $totalCount) * 100) : 0;
            @endphp

            <!-- Locked Filters Card -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-red-900">{{ $lockedCount }}</h3>
                        <p class="text-sm text-red-600">Locked Filters</p>
                    </div>
                </div>
            </div>

            <!-- Unlocked Filters Card -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-green-900">{{ $unlockedCount }}</h3>
                        <p class="text-sm text-green-600">Unlocked Filters</p>
                    </div>
                </div>
            </div>

            <!-- Premium Percentage Card -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-blue-900">{{ $premiumPercentage }}%</h3>
                        <p class="text-sm text-blue-600">Premium Filters</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter List -->
        @if($record)
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Filter Status Overview</h3>
                <p class="text-sm text-gray-600">Current status of all available filters</p>
            </div>
            <div class="divide-y divide-gray-200">
                @php
                    $filters = [
                        'working_hours_locked' => ['name' => 'Working Hours', 'description' => 'Open now filter'],
                        'rating_locked' => ['name' => 'Rating', 'description' => 'Rating filters (3.5+, 4+, 4.5+)'],
                        'family_friendly_locked' => ['name' => 'Family Friendly', 'description' => 'Family friendly places filter'],
                        'budget_locked' => ['name' => 'Budget', 'description' => 'Budget filters ($, $$, $$$)'],
                        'wheelchair_accessible_locked' => ['name' => 'Wheelchair Accessible', 'description' => 'Accessibility filter'],
                        'pet_friendly_locked' => ['name' => 'Pet Friendly', 'description' => 'Pet-friendly places filter'],
                        'sort_by_distance_locked' => ['name' => 'Sort by Distance', 'description' => 'Distance-based sorting'],
                    ];
                @endphp

                @foreach($filters as $field => $filter)
                <div class="px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($record->$field)
                                <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            @else
                                <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                                </svg>
                            @endif
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-gray-900">{{ $filter['name'] }}</h4>
                            <p class="text-sm text-gray-500">{{ $filter['description'] }}</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        @if($record->$field)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Premium Only
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Available to All
                            </span>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Form -->
        {{ $this->form }}
    </div>
</x-filament-panels::page>
