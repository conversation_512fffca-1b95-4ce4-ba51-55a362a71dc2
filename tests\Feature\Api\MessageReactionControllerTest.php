<?php

namespace Tests\Feature\Api;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\MessageReaction;
use App\Models\MessageReport;
use App\Models\User;
use App\Enums\ChatMessageSender;
use App\Enums\ReactionType;
use App\Enums\UserType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MessageReactionControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $otherUser;
    protected $agent;
    protected $chatSession;
    protected $chatMessage;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user (required for some tests)
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);

        // Create users for authentication
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();

        // Create an agent
        $this->agent = Agent::factory()->create();

        // Create a chat session
        $this->chatSession = ChatSession::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
        ]);

        // Create a chat message
        $this->chatMessage = ChatMessage::factory()->create([
            'chat_session_id' => $this->chatSession->id,
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'sender' => ChatMessageSender::AGENT,
            'message_text' => 'This is a test message from the agent.',
        ]);
    }

    /**
     * Test that unauthenticated users cannot store reactions.
     */
    public function test_unauthenticated_user_cannot_store_reaction(): void
    {
        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'like',
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test that authenticated users can store a like reaction.
     */
    public function test_authenticated_user_can_store_like_reaction(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'like',
        ]);

        $response->assertSuccessful()
            ->assertJsonStructure([
                'message',
                'success',
                'data' => [
                    'reaction',
                    'message_id',
                ]
            ])
            ->assertJsonPath('success', true)
            ->assertJsonPath('data.reaction', 'like')
            ->assertJsonPath('data.message_id', $this->chatMessage->id);

        // Verify reaction is stored in database
        $this->assertDatabaseHas('message_reactions', [
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'like',
        ]);
    }

    /**
     * Test that authenticated users can store a dislike reaction.
     */
    public function test_authenticated_user_can_store_dislike_reaction(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'dislike',
        ]);

        $response->assertSuccessful()
            ->assertJsonPath('data.reaction', 'dislike');

        // Verify reaction is stored in database
        $this->assertDatabaseHas('message_reactions', [
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'dislike',
        ]);
    }

    /**
     * Test that authenticated users can store a copy reaction.
     */
    public function test_authenticated_user_can_store_copy_reaction(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'copy',
        ]);

        $response->assertSuccessful()
            ->assertJsonPath('data.reaction', 'copy');

        // Verify reaction is stored in database with copied_at timestamp
        $reaction = MessageReaction::where([
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'copy',
        ])->first();

        $this->assertNotNull($reaction);
        $this->assertNotNull($reaction->copied_at);
    }

    /**
     * Test that existing reactions are updated instead of creating duplicates.
     */
    public function test_existing_reaction_is_updated(): void
    {
        Sanctum::actingAs($this->user);

        // Create initial reaction
        $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'like',
        ]);

        // Update to dislike
        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'dislike',
        ]);

        $response->assertSuccessful()
            ->assertJsonPath('data.reaction', 'dislike');

        // Verify only one reaction exists and it's updated
        $reactions = MessageReaction::where([
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
        ])->get();

        $this->assertCount(1, $reactions);
        $this->assertEquals('dislike', $reactions->first()->reaction->value);
    }

    /**
     * Test validation for required fields.
     */
    public function test_validation_for_required_fields(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['chat_message_id', 'reaction']);
    }

    /**
     * Test validation for invalid reaction type.
     */
    public function test_validation_for_invalid_reaction_type(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
            'reaction' => 'invalid_reaction',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['reaction']);
    }

    /**
     * Test validation for non-existent chat message.
     */
    public function test_validation_for_non_existent_chat_message(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/reaction', [
            'chat_message_id' => 999999,
            'reaction' => 'like',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['chat_message_id']);
    }

    /**
     * Test that unauthenticated users cannot remove reactions.
     */
    public function test_unauthenticated_user_cannot_remove_reaction(): void
    {
        $response = $this->deleteJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test that authenticated users can remove their reactions.
     */
    public function test_authenticated_user_can_remove_reaction(): void
    {
        Sanctum::actingAs($this->user);

        // First create a reaction
        MessageReaction::create([
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'agent_id' => $this->agent->id,
            'reaction' => 'like',
        ]);

        $response = $this->deleteJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
        ]);

        $response->assertSuccessful()
            ->assertJsonStructure([
                'message',
                'success',
            ])
            ->assertJsonPath('success', true);
    }

    /**
     * Test removing non-existent reaction returns 404.
     */
    public function test_removing_non_existent_reaction_returns_404(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->deleteJson('/api/chat/messages/reaction', [
            'chat_message_id' => $this->chatMessage->id,
        ]);

        $response->assertStatus(404)
            ->assertJsonPath('success', false);
    }

    /**
     * Test that unauthenticated users cannot store reports.
     */
    public function test_unauthenticated_user_cannot_store_report(): void
    {
        $response = $this->postJson('/api/chat/messages/report', [
            'chat_message_id' => $this->chatMessage->id,
            'report_reason' => 'Inappropriate content',
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test that authenticated users can store message reports.
     */
    public function test_authenticated_user_can_store_message_report(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/report', [
            'chat_message_id' => $this->chatMessage->id,
            'report_reason' => 'Inappropriate content',
        ]);

        $response->assertSuccessful()
            ->assertJsonStructure([
                'message',
                'success',
            ])
            ->assertJsonPath('success', true);

        // Verify report is stored in database
        $this->assertDatabaseHas('message_reports', [
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'report_reason' => 'Inappropriate content',
        ]);
    }

    /**
     * Test that users cannot report the same message twice.
     */
    public function test_user_cannot_report_same_message_twice(): void
    {
        Sanctum::actingAs($this->user);

        // Create initial report
        MessageReport::create([
            'user_id' => $this->user->id,
            'chat_message_id' => $this->chatMessage->id,
            'agent_id' => $this->agent->id,
            'report_reason' => 'First report',
        ]);

        $response = $this->postJson('/api/chat/messages/report', [
            'chat_message_id' => $this->chatMessage->id,
            'report_reason' => 'Second report',
        ]);

        $response->assertStatus(422)
            ->assertJsonPath('success', false);
    }

    /**
     * Test storing general report without specific message.
     */
    public function test_can_store_general_report_without_message(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/report', [
            'chat_session_id' => $this->chatSession->id,
            'agent_id' => $this->agent->id,
            'report_reason' => 'General conversation issue',
        ]);

        $response->assertSuccessful()
            ->assertJsonPath('success', true);

        // Verify report is stored in database
        $this->assertDatabaseHas('message_reports', [
            'user_id' => $this->user->id,
            'chat_session_id' => $this->chatSession->id,
            'agent_id' => $this->agent->id,
            'report_reason' => 'General conversation issue',
            'chat_message_id' => null,
        ]);
    }

    /**
     * Test validation for report without required fields.
     */
    public function test_validation_for_report_without_required_fields(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/chat/messages/report', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['report_reason']);
    }
}
