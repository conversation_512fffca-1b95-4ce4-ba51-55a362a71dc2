<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FilterAccessControl;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * Controller for managing filter access control from admin dashboard
 */
class FilterAccessControlController extends Controller
{
    /**
     * Get current filter access control settings
     */
    public function index(): JsonResponse
    {
        $filterAccess = FilterAccessControl::first();
        
        if (!$filterAccess) {
            // Create default settings if none exist
            $filterAccess = FilterAccessControl::create([
                'working_hours_locked' => false,
                'rating_locked' => false,
                'family_friendly_locked' => false,
                'budget_locked' => false,
                'wheelchair_accessible_locked' => false,
                'pet_friendly_locked' => false,
                'sort_by_distance_locked' => false,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $filterAccess,
            'message' => 'Filter access control settings retrieved successfully'
        ]);
    }

    /**
     * Update filter access control settings
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'working_hours_locked' => 'boolean',
            'rating_locked' => 'boolean',
            'family_friendly_locked' => 'boolean',
            'budget_locked' => 'boolean',
            'wheelchair_accessible_locked' => 'boolean',
            'pet_friendly_locked' => 'boolean',
            'sort_by_distance_locked' => 'boolean',
        ]);

        $filterAccess = FilterAccessControl::first();
        
        if (!$filterAccess) {
            $filterAccess = new FilterAccessControl();
        }

        $filterAccess->update([
            'working_hours_locked' => $request->get('working_hours_locked', false),
            'rating_locked' => $request->get('rating_locked', false),
            'family_friendly_locked' => $request->get('family_friendly_locked', false),
            'budget_locked' => $request->get('budget_locked', false),
            'wheelchair_accessible_locked' => $request->get('wheelchair_accessible_locked', false),
            'pet_friendly_locked' => $request->get('pet_friendly_locked', false),
            'sort_by_distance_locked' => $request->get('sort_by_distance_locked', false),
        ]);

        return response()->json([
            'success' => true,
            'data' => $filterAccess,
            'message' => 'Filter access control settings updated successfully'
        ]);
    }

    /**
     * Reset all filters to unlocked (free access)
     */
    public function reset(): JsonResponse
    {
        $filterAccess = FilterAccessControl::first();
        
        if (!$filterAccess) {
            $filterAccess = new FilterAccessControl();
        }

        $filterAccess->update([
            'working_hours_locked' => false,
            'rating_locked' => false,
            'family_friendly_locked' => false,
            'budget_locked' => false,
            'wheelchair_accessible_locked' => false,
            'pet_friendly_locked' => false,
            'sort_by_distance_locked' => false,
        ]);

        return response()->json([
            'success' => true,
            'data' => $filterAccess,
            'message' => 'All filters have been unlocked successfully'
        ]);
    }

    /**
     * Lock all filters (premium access required)
     */
    public function lockAll(): JsonResponse
    {
        $filterAccess = FilterAccessControl::first();
        
        if (!$filterAccess) {
            $filterAccess = new FilterAccessControl();
        }

        $filterAccess->update([
            'working_hours_locked' => true,
            'rating_locked' => true,
            'family_friendly_locked' => true,
            'budget_locked' => true,
            'wheelchair_accessible_locked' => true,
            'pet_friendly_locked' => true,
            'sort_by_distance_locked' => true,
        ]);

        return response()->json([
            'success' => true,
            'data' => $filterAccess,
            'message' => 'All filters have been locked successfully'
        ]);
    }

    /**
     * Toggle a specific filter lock status
     */
    public function toggleFilter(Request $request): JsonResponse
    {
        $request->validate([
            'filter_type' => 'required|string|in:working_hours,rating,family_friendly,budget,wheelchair_accessible,pet_friendly,sort_by_distance'
        ]);

        $filterAccess = FilterAccessControl::first();
        
        if (!$filterAccess) {
            $filterAccess = FilterAccessControl::create([
                'working_hours_locked' => false,
                'rating_locked' => false,
                'family_friendly_locked' => false,
                'budget_locked' => false,
                'wheelchair_accessible_locked' => false,
                'pet_friendly_locked' => false,
                'sort_by_distance_locked' => false,
            ]);
        }

        $filterType = $request->get('filter_type') . '_locked';
        $currentStatus = $filterAccess->{$filterType};
        
        $filterAccess->update([
            $filterType => !$currentStatus
        ]);

        return response()->json([
            'success' => true,
            'data' => $filterAccess,
            'message' => "Filter {$request->get('filter_type')} has been " . (!$currentStatus ? 'locked' : 'unlocked')
        ]);
    }
}
