<?php

namespace App\Services\AI;

class AIServiceFactory
{
    public static function create(string $provider = null): AIServiceInterface
    {
        /** @todo enhance to use face for mock test $provider */
//        if (app()->bound(AIServiceInterface::class)) {
//            return app(AIServiceInterface::class);
//        }

        $provider = $provider ?? config('services.ai.default_provider', 'openai');

        /** @todo enhance to choose provider ai based agent provider */
        return match ($provider) {
            'openai' => new OpenAIService(),
            // Add more providers here as needed
            // 'anthropic' => new AnthropicService(),
            // 'gemini' => new GeminiService(),
            default => throw new \InvalidArgumentException("Unsupported AI provider: {$provider}"),
        };
    }
}
