<?php

namespace Tests\Feature\Api;

use App\Enums\UserType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class SearchControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user (required for some tests)
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);

        // Create a user for authentication
        $this->user = User::factory()->create();
    }

    /**
     * Test that unauthenticated users cannot access the search endpoint.
     */
    public function test_unauthenticated_user_cannot_access_search(): void
    {
        $response = $this->postJson('api/places/search', [
            'prompt' => 'restaurants in Dubai',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test successful search with valid data.
     */
    public function test_can_search_with_valid_data(): void
    {
        Sanctum::actingAs($this->user);

        $searchData = [
            'prompt' => 'restaurants in Dubai',
            'language' => 'en',
        ];

        $response = $this->postJson('api/places/search', $searchData, [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful()
            ->assertJsonStructure([
                'category' => [
                    'id',
                    'name'
                ],
                'locations' => [
                    '*' => [
                        'name',
                        'details',
                        'address',
                        'category',
                        'formatted_address',
                        'maps_url',
                        'photos',
                        'warning_location',
                        'google_name',
                        'google_places' => [
                            'name',
                            'details',
                            'address',
                            'formatted_address',
                            'maps_url',
                            'photos',
                            'latitude',
                            'longitude',
                            'rating',
                            'types',
                            'opening_hours' => [
                                'open_now',
                                'periods',
                                'weekday_text'
                            ],
                            'website',
                            'formatted_phone_number',
                            'price_level',
                            'reviews',
                            'place_id',
                            'warning_location',
                            'google_name',
                            'id',
                            'is_favorited'
                        ]
                    ]
                ],
                'raw_response'
            ]);

        // Verify the response contains expected data
        $responseData = $response->json();

        $this->assertEquals(2, $responseData['category']['id']);
        $this->assertEquals('Bites', $responseData['category']['name']);
        $this->assertCount(3, $responseData['locations']);
        $this->assertStringContainsString('restaurants in Dubai', $responseData['raw_response']);
        $this->assertStringContainsString('25.2048', $responseData['raw_response']);
        $this->assertStringContainsString('55.2708', $responseData['raw_response']);
    }

    /**
     * Test search with minimal required data (no language specified).
     */
    public function test_can_search_with_minimal_data(): void
    {
        Sanctum::actingAs($this->user);

        $searchData = [
            'prompt' => 'cafes',
        ];

        $response = $this->postJson('api/places/search', $searchData, [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $this->assertStringContainsString('cafes', $responseData['raw_response']);
        $this->assertStringContainsString('en', $responseData['raw_response']); // Default language
    }

    /**
     * Test search with Arabic language.
     */
    public function test_can_search_with_arabic_language(): void
    {
        Sanctum::actingAs($this->user);

        $searchData = [
            'prompt' => 'مطاعم في دبي',
            'language' => 'ar',
        ];

        $response = $this->postJson('api/places/search', $searchData, [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $this->assertStringContainsString('مطاعم في دبي', $responseData['raw_response']);
        $this->assertStringContainsString('ar', $responseData['raw_response']);
    }

    /**
     * Test validation errors for missing required fields.
     */
    public function test_validates_required_fields(): void
    {
        Sanctum::actingAs($this->user);

        // Test missing prompt
        $response = $this->postJson('api/places/search', [], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['prompt']);
    }

    /**
     * Test validation for invalid data types and ranges.
     */
    public function test_validates_data_types_and_ranges(): void
    {
        Sanctum::actingAs($this->user);

        // Test prompt too long
        $response = $this->postJson('api/places/search', [
            'prompt' => str_repeat('a', 256), // Invalid: > 255 characters
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['prompt']);
    }

    /**
     * Test that the response contains all expected locations.
     */
    public function test_response_contains_expected_locations(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('api/places/search', [
            'prompt' => 'restaurants in Dubai',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Check that we have the expected 3 locations
        $this->assertCount(3, $locations);

        // Check specific location names
        $locationNames = array_column($locations, 'name');
        $this->assertContains('**Al Fanar Restaurant & Café**', $locationNames);
        $this->assertContains('**Burj Khalifa Restaurant**', $locationNames);
        $this->assertContains('**Dubai Mall Food Court**', $locationNames);

        // Check that each location has required google_places data
        foreach ($locations as $location) {
            $this->assertArrayHasKey('google_places', $location);
            $googlePlaces = $location['google_places'];

            $this->assertArrayHasKey('latitude', $googlePlaces);
            $this->assertArrayHasKey('longitude', $googlePlaces);
            $this->assertArrayHasKey('rating', $googlePlaces);
            $this->assertArrayHasKey('opening_hours', $googlePlaces);
            $this->assertArrayHasKey('reviews', $googlePlaces);
            $this->assertArrayHasKey('place_id', $googlePlaces);
        }
    }

    /**
     * Test that coordinates are properly formatted as numbers.
     */
    public function test_coordinates_are_properly_formatted(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('api/places/search', [
            'prompt' => 'restaurants',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        foreach ($locations as $location) {
            $googlePlaces = $location['google_places'];

            $this->assertIsFloat($googlePlaces['latitude']);
            $this->assertIsFloat($googlePlaces['longitude']);
            $this->assertIsFloat($googlePlaces['rating']);
            $this->assertIsInt($googlePlaces['price_level']);
        }
    }

    /**
     * Test search query filtering - should return matching results.
     */
    public function test_search_query_filtering_returns_matching_results(): void
    {
        Sanctum::actingAs($this->user);

        // Search for "Al Fanar" - should return only Al Fanar restaurant
        $response = $this->postJson('api/places/search', [
            'prompt' => 'Al Fanar',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Should return only 1 location (Al Fanar)
        $this->assertCount(1, $locations);
        $this->assertStringContainsString('Al Fanar', $locations[0]['name']);
    }

    /**
     * Test search query filtering - should return empty results for non-matching query.
     */
    public function test_search_query_filtering_returns_empty_for_no_matches(): void
    {
        Sanctum::actingAs($this->user);

        // Search for something that doesn't exist in static data
        $response = $this->postJson('api/places/search', [
            'prompt' => 'NonExistentPlace12345',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Should return empty array
        $this->assertCount(0, $locations);
        $this->assertEmpty($locations);
    }

    /**
     * Test search query filtering is case-insensitive.
     */
    public function test_search_query_filtering_is_case_insensitive(): void
    {
        Sanctum::actingAs($this->user);

        // Search for "burj khalifa" in lowercase - should still match "Burj Khalifa Restaurant"
        $response = $this->postJson('api/places/search', [
            'prompt' => 'burj khalifa',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Should return 1 location (Burj Khalifa Restaurant)
        $this->assertCount(1, $locations);
        $this->assertStringContainsString('Burj Khalifa', $locations[0]['name']);
    }

    /**
     * Test search query filtering searches multiple fields.
     */
    public function test_search_query_filtering_searches_multiple_fields(): void
    {
        Sanctum::actingAs($this->user);

        // Search for "Dubai Mall" - should match both address and name fields
        $response = $this->postJson('api/places/search', [
            'prompt' => 'Dubai Mall',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Should return locations that contain "Dubai Mall" in any field
        $this->assertGreaterThan(0, count($locations));

        // Check that at least one location contains "Dubai Mall" in name or address
        $foundMatch = false;
        foreach ($locations as $location) {
            if (stripos($location['name'], 'Dubai Mall') !== false ||
                stripos($location['address'], 'Dubai Mall') !== false ||
                stripos($location['formatted_address'], 'Dubai Mall') !== false) {
                $foundMatch = true;
                break;
            }
        }
        $this->assertTrue($foundMatch, 'Should find at least one location matching "Dubai Mall"');
    }

    /**
     * Test that empty prompt returns all locations.
     */
    public function test_empty_prompt_returns_all_locations(): void
    {
        Sanctum::actingAs($this->user);

        // Test with empty string
        $response = $this->postJson('api/places/search', [
            'prompt' => '',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response->assertSuccessful();

        $responseData = $response->json();
        $locations = $responseData['locations'];

        // Should return all locations from static data (currently 5 locations)
        $this->assertGreaterThan(0, count($locations));

        // Test with whitespace only
        $response2 = $this->postJson('api/places/search', [
            'prompt' => '   ',
        ], [
            'X-Lat' => '25.2048',
            'X-Lng' => '55.2708',
        ]);

        $response2->assertSuccessful();

        $responseData2 = $response2->json();
        $locations2 = $responseData2['locations'];

        // Should also return all locations
        $this->assertEquals(count($locations), count($locations2));
    }
}
