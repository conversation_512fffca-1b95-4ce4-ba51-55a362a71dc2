<?php

namespace App\Filament\Resources\FilterAccessControlResource\Pages;

use App\Filament\Resources\FilterAccessControlResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditFilterAccessControl extends EditRecord
{
    protected static string $resource = FilterAccessControlResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('lockAll')
                ->label('Lock All Filters')
                ->icon('heroicon-o-lock-closed')
                ->color('danger')
                ->requiresConfirmation()
                ->modalDescription('This will lock all filters for premium users only.')
                ->action(function () {
                    $this->record->update([
                        'working_hours_locked' => true,
                        'rating_locked' => true,
                        'family_friendly_locked' => true,
                        'budget_locked' => true,
                        'wheelchair_accessible_locked' => true,
                        'pet_friendly_locked' => true,
                        'sort_by_distance_locked' => true,
                    ]);

                    Notification::make()
                        ->success()
                        ->title('All Filters Locked')
                        ->body('All filters are now locked for premium users only.')
                        ->send();

                    $this->fillForm();
                }),

            Actions\Action::make('unlockAll')
                ->label('Unlock All Filters')
                ->icon('heroicon-o-lock-open')
                ->color('success')
                ->requiresConfirmation()
                ->modalDescription('This will unlock all filters for all users.')
                ->action(function () {
                    $this->record->update([
                        'working_hours_locked' => false,
                        'rating_locked' => false,
                        'family_friendly_locked' => false,
                        'budget_locked' => false,
                        'wheelchair_accessible_locked' => false,
                        'pet_friendly_locked' => false,
                        'sort_by_distance_locked' => false,
                    ]);

                    Notification::make()
                        ->success()
                        ->title('All Filters Unlocked')
                        ->body('All filters are now available for all users.')
                        ->send();

                    $this->fillForm();
                }),

            Actions\Action::make('toggleWorkingHours')
                ->label('Toggle Working Hours')
                ->icon('heroicon-o-clock')
                ->color('info')
                ->action(function () {
                    $this->record->update([
                        'working_hours_locked' => !$this->record->working_hours_locked,
                    ]);

                    $status = $this->record->working_hours_locked ? 'locked' : 'unlocked';
                    
                    Notification::make()
                        ->success()
                        ->title('Working Hours Filter Updated')
                        ->body("Working hours filter is now {$status}.")
                        ->send();

                    $this->fillForm();
                }),

            Actions\Action::make('toggleRating')
                ->label('Toggle Rating')
                ->icon('heroicon-o-star')
                ->color('info')
                ->action(function () {
                    $this->record->update([
                        'rating_locked' => !$this->record->rating_locked,
                    ]);

                    $status = $this->record->rating_locked ? 'locked' : 'unlocked';
                    
                    Notification::make()
                        ->success()
                        ->title('Rating Filter Updated')
                        ->body("Rating filter is now {$status}.")
                        ->send();

                    $this->fillForm();
                }),

            Actions\DeleteAction::make()
                ->visible(false), // Hide delete action as we want to keep the record
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
