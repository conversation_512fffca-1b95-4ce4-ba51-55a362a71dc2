<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SearchRequest;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    /**
     * Handle search requests with filters
     *
     * @param SearchRequest $request
     * @return JsonResponse
     */
    public function search(SearchRequest $request): JsonResponse
    {
        // Get validated data
        $query = $request->input('prompt');

        // Get latitude and longitude from headers
        $latitude = $request->header('X-Lat');
        $longitude = $request->header('X-Lng');

        // Get static data
        $staticLocations = $this->getStaticLocations();

        // Apply filters to static data
        $filteredLocations = $this->applyFiltersToStaticData($staticLocations, $request);

        // Build response
        $response = [
            'category' => [
                'id' => 2,
                'name' => 'Search Results'
            ],
            'locations' => $filteredLocations,
            'raw_response' => $this->generateRawResponse($query, $filteredLocations, $latitude, $longitude)
        ];

        return response()->json($response);
    }

    /**
     * Get static locations data with comprehensive test data
     */
    private function getStaticLocations(): array
    {
        return [
            // Restaurant 1 - Al Fanar (Bites Category)
            [
                'name' => '**Al Fanar Restaurant & Café**',
                'details' => "- **Type**: Traditional Emirati Cuisine\n   - **Address**: Dubai Festival City Mall\n   - **Key Features**: Offers a traditional Emirati dining experience with a cozy atmosphere.\n   - **Coordinates**: 25.2221871, 55.3517491",
                'address' => 'Dubai Festival City Mall',
                'category' => 'Bites',
                'formatted_address' => 'Ground Floor, Dubai Festival City Mall, Near P.F.Chang\'s, Canal Walk - Dubai Festival City - Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=6481672121910019428',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=AXQCQNRdKcxjK7Uq0TGUbvUUEnOGBFcHOOXl-0Ai5OHcxC3TS8XQx1CIWdrjwh9q-SLaRGZEZEux-VYqW-l5jo09GWARNYOR2u7fVfqex-g2ow0aVNM87mhn95oBPwtvcy-h4STwl4pd1Q3oamPqJDmO7gADtm0R5nHDGUXaAhtHBX-nPvgmWdcD0mjg3lo_czti_MmlYeN-8o3Vbu1IbhSdUdPiWrQY1v80uppf3eyUZgjS4l7-N8N8utk8IOujftHLdurBIXx4CwAJaA4TQVK1ffAVlP-B2hF3hr5Wg90aFZEIsuDMreUPk5ZPPVmPqYCfQbQ1dHWR-Q8&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Al Fanar',
                'google_places' => [
                    'name' => 'Al Fanar',
                    'details' => "- **Type:** Cafe\n   - **Address:** Ground Floor, Dubai Festival City Mall\n   - **Key Features:** Rated 4.6 stars, Price level: $$, Currently open\n   - **Coordinates:** Approximately 25.2221871, 55.3517491",
                    'address' => '',
                    'formatted_address' => 'Ground Floor, Dubai Festival City Mall, Near P.F.Chang\'s, Canal Walk - Dubai Festival City - Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=6481672121910019428',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=sample1&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.2221871,
                    'longitude' => 55.35174910000001,
                    'rating' => 4.6,
                    'types' => [
                        'cafe',
                        'restaurant',
                        'food',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => true,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '2200'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '0900'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 9:00 AM – 10:00 PM',
                            'Tuesday: 9:00 AM – 10:00 PM',
                            'Wednesday: 9:00 AM – 10:00 PM',
                            'Thursday: 9:00 AM – 10:00 PM',
                            'Friday: 9:00 AM – 10:00 PM',
                            'Saturday: 9:00 AM – 10:00 PM',
                            'Sunday: 9:00 AM – 10:00 PM'
                        ]
                    ],
                    'website' => 'https://www.alfanarrestaurant.com/',
                    'formatted_phone_number' => '04 232 9966',
                    'price_level' => 2,
                    'reviews' => [
                        [
                            'author_name' => 'Jeremy Meacham',
                            'rating' => 5,
                            'text' => 'Excellent and welcoming to foreigners. Staff spoke English, amazing service!',
                            'time' => 1745148639
                        ]
                    ],
                    'place_id' => 'ChIJad3dmXtdXz4RZMX_GHSG81k',
                    'warning_location' => false,
                    'google_name' => 'Al Fanar',
                    'id' => 106,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => true,
                    'pet_friendly' => false,
                    'category_id' => 2 // Bites category
                ]
            ],

            // Restaurant 2 - Burj Khalifa (Bites Category - Fine Dining)
            [
                'name' => '**Burj Khalifa Restaurant**',
                'details' => "- **Type**: Fine Dining\n   - **Address**: Downtown Dubai, Burj Khalifa\n   - **Key Features**: Offers spectacular views of Dubai skyline with international cuisine.\n   - **Coordinates**: 25.1972, 55.2744",
                'address' => 'Downtown Dubai',
                'category' => 'Bites',
                'formatted_address' => 'Burj Khalifa, Downtown Dubai - Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=1234567890123456789',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=sample2&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Burj Khalifa Restaurant',
                'google_places' => [
                    'name' => 'Burj Khalifa Restaurant',
                    'details' => "- **Type:** Fine Dining\n   - **Address:** Downtown Dubai, Burj Khalifa\n   - **Key Features:** Rated 4.8 stars, Price level: $$$$, Currently open\n   - **Coordinates:** 25.1972, 55.2744",
                    'address' => 'Downtown Dubai',
                    'formatted_address' => 'Burj Khalifa, Downtown Dubai - Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=1234567890123456789',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=sample2&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.1972,
                    'longitude' => 55.2744,
                    'rating' => 4.8,
                    'types' => [
                        'restaurant',
                        'food',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => true,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '2300'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '1200'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 12:00 PM – 11:00 PM',
                            'Tuesday: 12:00 PM – 11:00 PM',
                            'Wednesday: 12:00 PM – 11:00 PM',
                            'Thursday: 12:00 PM – 11:00 PM',
                            'Friday: 12:00 PM – 11:00 PM',
                            'Saturday: 12:00 PM – 11:00 PM',
                            'Sunday: 12:00 PM – 11:00 PM'
                        ]
                    ],
                    'website' => 'https://www.burjkhalifa.ae/restaurant',
                    'formatted_phone_number' => '04 888 3828',
                    'price_level' => 4,
                    'reviews' => [
                        [
                            'author_name' => 'Sarah Johnson',
                            'rating' => 5,
                            'text' => 'Amazing dining experience with breathtaking views of Dubai.',
                            'time' => 1746000000
                        ]
                    ],
                    'place_id' => 'ChIJSamplePlaceId123456789',
                    'warning_location' => false,
                    'google_name' => 'Burj Khalifa Restaurant',
                    'id' => 107,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => true,
                    'pet_friendly' => false,
                    'category_id' => 2 // Bites category
                ]
            ],
            [
                'name' => '**Dubai Mall Food Court**',
                'details' => "- **Type**: Food Court\n   - **Address**: Dubai Mall, Downtown Dubai\n   - **Key Features**: Variety of international cuisines in a modern shopping mall setting.\n   - **Coordinates**: 25.1975, 55.2796",
                'address' => 'Dubai Mall',
                'category' => 'Bites',
                'formatted_address' => 'Dubai Mall, Downtown Dubai - Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=9876543210987654321',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=sample3&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Dubai Mall Food Court',
                'google_places' => [
                    'name' => 'Dubai Mall Food Court',
                    'details' => "- **Type:** Food Court\n   - **Address:** Dubai Mall, Downtown Dubai\n   - **Key Features:** Rated 4.2 stars, Price level: $$, Currently closed\n   - **Coordinates:** 25.1975, 55.2796",
                    'address' => 'Dubai Mall',
                    'formatted_address' => 'Dubai Mall, Downtown Dubai - Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=9876543210987654321',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=sample3&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.1975,
                    'longitude' => 55.2796,
                    'rating' => 4.2,
                    'types' => [
                        'food',
                        'meal_takeaway',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => false,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '2400'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '1000'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 10:00 AM – 12:00 AM',
                            'Tuesday: 10:00 AM – 12:00 AM',
                            'Wednesday: 10:00 AM – 12:00 AM',
                            'Thursday: 10:00 AM – 12:00 AM',
                            'Friday: 10:00 AM – 12:00 AM',
                            'Saturday: 10:00 AM – 12:00 AM',
                            'Sunday: 10:00 AM – 12:00 AM'
                        ]
                    ],
                    'website' => 'https://www.thedubaimall.com/dining',
                    'formatted_phone_number' => '04 362 7500',
                    'price_level' => 2,
                    'reviews' => [
                        [
                            'author_name' => 'Ahmed Al-Rashid',
                            'rating' => 4,
                            'text' => 'Great variety of food options. Clean and well-maintained food court.',
                            'time' => 1747000000
                        ]
                    ],
                    'place_id' => 'ChIJAnotherSamplePlaceId789',
                    'warning_location' => false,
                    'google_name' => 'Dubai Mall Food Court',
                    'id' => 108,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => true,
                    'pet_friendly' => true,
                    'category_id' => 2 // Bites category
                ]
            ],

            // Adventure Location 1 - Desert Safari (Adventures Category)
            [
                'name' => '**Dubai Desert Safari Adventure**',
                'details' => "- **Type**: Desert Adventure\n   - **Address**: Al Awir Desert, Dubai\n   - **Key Features**: Thrilling desert safari with dune bashing, camel riding, and traditional entertainment.\n   - **Coordinates**: 25.0657, 55.4781",
                'address' => 'Al Awir Desert',
                'category' => 'Adventures',
                'formatted_address' => 'Al Awir Desert, Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=1111111111111111111',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=desert_safari_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Dubai Desert Safari',
                'google_places' => [
                    'name' => 'Dubai Desert Safari Adventure',
                    'details' => "- **Type:** Adventure Tour\n   - **Address:** Al Awir Desert, Dubai\n   - **Key Features:** Rated 4.7 stars, Price level: $$$, Currently open\n   - **Coordinates:** 25.0657, 55.4781",
                    'address' => 'Al Awir Desert',
                    'formatted_address' => 'Al Awir Desert, Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=1111111111111111111',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=desert_safari_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.0657,
                    'longitude' => 55.4781,
                    'rating' => 4.7,
                    'types' => [
                        'tourist_attraction',
                        'travel_agency',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => true,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '2200'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '0600'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 6:00 AM – 10:00 PM',
                            'Tuesday: 6:00 AM – 10:00 PM',
                            'Wednesday: 6:00 AM – 10:00 PM',
                            'Thursday: 6:00 AM – 10:00 PM',
                            'Friday: 6:00 AM – 10:00 PM',
                            'Saturday: 6:00 AM – 10:00 PM',
                            'Sunday: 6:00 AM – 10:00 PM'
                        ]
                    ],
                    'website' => 'https://www.dubaidesertadventure.com/',
                    'formatted_phone_number' => '04 123 4567',
                    'price_level' => 3,
                    'reviews' => [
                        [
                            'author_name' => 'Adventure Seeker',
                            'rating' => 5,
                            'text' => 'Amazing desert experience! The dune bashing was thrilling and the sunset views were breathtaking.',
                            'time' => 1748000000
                        ]
                    ],
                    'place_id' => 'ChIJDesertSafariPlaceId123',
                    'warning_location' => false,
                    'google_name' => 'Dubai Desert Safari',
                    'id' => 201,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => false,
                    'pet_friendly' => false,
                    'category_id' => 1 // Adventures category
                ]
            ],

            // Venture Location 1 - Business Center (Ventures Category)
            [
                'name' => '**Dubai International Financial Centre**',
                'details' => "- **Type**: Business & Financial Hub\n   - **Address**: DIFC, Dubai\n   - **Key Features**: Premier financial district with world-class business facilities and networking opportunities.\n   - **Coordinates**: 25.2138, 55.2796",
                'address' => 'DIFC',
                'category' => 'Ventures',
                'formatted_address' => 'Dubai International Financial Centre, Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=2222222222222222222',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=difc_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'DIFC',
                'google_places' => [
                    'name' => 'Dubai International Financial Centre',
                    'details' => "- **Type:** Business District\n   - **Address:** DIFC, Dubai\n   - **Key Features:** Rated 4.5 stars, Price level: $$$$, Currently open\n   - **Coordinates:** 25.2138, 55.2796",
                    'address' => 'DIFC',
                    'formatted_address' => 'Dubai International Financial Centre, Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=2222222222222222222',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=difc_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.2138,
                    'longitude' => 55.2796,
                    'rating' => 4.5,
                    'types' => [
                        'establishment',
                        'finance',
                        'point_of_interest'
                    ],
                    'opening_hours' => [
                        'open_now' => true,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '1800'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '0800'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 8:00 AM – 6:00 PM',
                            'Tuesday: 8:00 AM – 6:00 PM',
                            'Wednesday: 8:00 AM – 6:00 PM',
                            'Thursday: 8:00 AM – 6:00 PM',
                            'Friday: 8:00 AM – 6:00 PM',
                            'Saturday: Closed',
                            'Sunday: Closed'
                        ]
                    ],
                    'website' => 'https://www.difc.ae/',
                    'formatted_phone_number' => '04 362 2222',
                    'price_level' => 4,
                    'reviews' => [
                        [
                            'author_name' => 'Business Professional',
                            'rating' => 5,
                            'text' => 'Excellent business environment with top-notch facilities and great networking opportunities.',
                            'time' => 1749000000
                        ]
                    ],
                    'place_id' => 'ChIJDIFCPlaceId456',
                    'warning_location' => false,
                    'google_name' => 'DIFC',
                    'id' => 301,
                    'is_favorited' => false,
                    'family_friendly' => false,
                    'wheelchair_accessible' => true,
                    'pet_friendly' => false,
                    'category_id' => 3 // Ventures category
                ]
            ],

            // Needs Location 1 - Pharmacy (Needs Category)
            [
                'name' => '**Life Pharmacy Dubai Mall**',
                'details' => "- **Type**: Pharmacy & Health Store\n   - **Address**: Dubai Mall, Downtown Dubai\n   - **Key Features**: 24/7 pharmacy with wide range of medications, health products, and wellness items.\n   - **Coordinates**: 25.1975, 55.2796",
                'address' => 'Dubai Mall',
                'category' => 'Needs',
                'formatted_address' => 'Dubai Mall, Downtown Dubai - Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=3333333333333333333',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=pharmacy_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Life Pharmacy',
                'google_places' => [
                    'name' => 'Life Pharmacy Dubai Mall',
                    'details' => "- **Type:** Pharmacy\n   - **Address:** Dubai Mall, Downtown Dubai\n   - **Key Features:** Rated 4.3 stars, Price level: $$, Currently open 24/7\n   - **Coordinates:** 25.1975, 55.2796",
                    'address' => 'Dubai Mall',
                    'formatted_address' => 'Dubai Mall, Downtown Dubai - Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=3333333333333333333',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=pharmacy_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.1975,
                    'longitude' => 55.2796,
                    'rating' => 4.3,
                    'types' => [
                        'pharmacy',
                        'health',
                        'store',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => true,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '2359'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '0000'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: Open 24 hours',
                            'Tuesday: Open 24 hours',
                            'Wednesday: Open 24 hours',
                            'Thursday: Open 24 hours',
                            'Friday: Open 24 hours',
                            'Saturday: Open 24 hours',
                            'Sunday: Open 24 hours'
                        ]
                    ],
                    'website' => 'https://www.lifepharmacy.com/',
                    'formatted_phone_number' => '04 339 8888',
                    'price_level' => 2,
                    'reviews' => [
                        [
                            'author_name' => 'Health Conscious',
                            'rating' => 4,
                            'text' => 'Great pharmacy with helpful staff and good selection of health products. Open 24/7 which is very convenient.',
                            'time' => **********
                        ]
                    ],
                    'place_id' => 'ChIJLifePharmacyPlaceId789',
                    'warning_location' => false,
                    'google_name' => 'Life Pharmacy',
                    'id' => 401,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => true,
                    'pet_friendly' => false,
                    'category_id' => 4 // Needs category
                ]
            ],

            // Additional Bites Location - Budget Friendly
            [
                'name' => '**Shawarma Palace**',
                'details' => "- **Type**: Middle Eastern Fast Food\n   - **Address**: Deira, Dubai\n   - **Key Features**: Authentic shawarma and Middle Eastern cuisine at affordable prices.\n   - **Coordinates**: 25.2697, 55.3095",
                'address' => 'Deira',
                'category' => 'Bites',
                'formatted_address' => 'Deira, Dubai - United Arab Emirates',
                'maps_url' => 'https://maps.google.com/?cid=4444444444444444444',
                'photos' => [
                    'https://maps.googleapis.com/maps/api/place/photo?photoreference=shawarma_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                ],
                'warning_location' => false,
                'google_name' => 'Shawarma Palace',
                'google_places' => [
                    'name' => 'Shawarma Palace',
                    'details' => "- **Type:** Fast Food Restaurant\n   - **Address:** Deira, Dubai\n   - **Key Features:** Rated 3.8 stars, Price level: $, Currently closed\n   - **Coordinates:** 25.2697, 55.3095",
                    'address' => 'Deira',
                    'formatted_address' => 'Deira, Dubai - United Arab Emirates',
                    'maps_url' => 'https://maps.google.com/?cid=4444444444444444444',
                    'photos' => [
                        'https://maps.googleapis.com/maps/api/place/photo?photoreference=shawarma_photo&maxwidth=400&key=AIzaSyCWdQnNbTAlEuxohdsfY1YlwB5gppY8PW0'
                    ],
                    'latitude' => 25.2697,
                    'longitude' => 55.3095,
                    'rating' => 3.8,
                    'types' => [
                        'restaurant',
                        'meal_takeaway',
                        'food',
                        'point_of_interest',
                        'establishment'
                    ],
                    'opening_hours' => [
                        'open_now' => false,
                        'periods' => [
                            [
                                'close' => [
                                    'day' => 0,
                                    'time' => '0200'
                                ],
                                'open' => [
                                    'day' => 0,
                                    'time' => '1100'
                                ]
                            ]
                        ],
                        'weekday_text' => [
                            'Monday: 11:00 AM – 2:00 AM',
                            'Tuesday: 11:00 AM – 2:00 AM',
                            'Wednesday: 11:00 AM – 2:00 AM',
                            'Thursday: 11:00 AM – 2:00 AM',
                            'Friday: 11:00 AM – 2:00 AM',
                            'Saturday: 11:00 AM – 2:00 AM',
                            'Sunday: 11:00 AM – 2:00 AM'
                        ]
                    ],
                    'website' => null,
                    'formatted_phone_number' => '04 226 7890',
                    'price_level' => 1,
                    'reviews' => [
                        [
                            'author_name' => 'Food Lover',
                            'rating' => 4,
                            'text' => 'Great value for money! Authentic Middle Eastern flavors and generous portions.',
                            'time' => 1751000000
                        ]
                    ],
                    'place_id' => 'ChIJShawarmaPlaceId012',
                    'warning_location' => false,
                    'google_name' => 'Shawarma Palace',
                    'id' => 109,
                    'is_favorited' => false,
                    'family_friendly' => true,
                    'wheelchair_accessible' => false,
                    'pet_friendly' => true,
                    'category_id' => 2 // Bites category
                ]
            ]
        ];
    }

    /**
     * Apply filters to static data
     */
    private function applyFiltersToStaticData(array $locations, SearchRequest $request): array
    {
        $filteredLocations = $locations;

        // Apply search query filter first
        $query = $request->input('prompt');
        if (!empty($query) && trim($query) !== '') {
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($query) {
                $searchQuery = strtolower(trim($query));

                // Search in multiple fields
                $searchFields = [
                    $location['name'] ?? '',
                    $location['details'] ?? '',
                    $location['address'] ?? '',
                    $location['category'] ?? '',
                    $location['formatted_address'] ?? '',
                    $location['google_name'] ?? '',
                    $location['google_places']['name'] ?? '',
                    $location['google_places']['details'] ?? '',
                    $location['google_places']['address'] ?? '',
                    $location['google_places']['formatted_address'] ?? '',
                ];

                // Also search in types array if it exists
                if (isset($location['google_places']['types']) && is_array($location['google_places']['types'])) {
                    $searchFields = array_merge($searchFields, $location['google_places']['types']);
                }

                // Check if query matches any of the search fields
                foreach ($searchFields as $field) {
                    if (stripos($field, $searchQuery) !== false) {
                        return true;
                    }
                }

                return false;
            });
        }
        // If prompt is empty or only whitespace, return all locations (no filtering by search query)

        // Apply rating filter
        if ($request->has('rating')) {
            $ratingFilter = $request->input('rating');
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($ratingFilter) {
                $rating = $location['google_places']['rating'] ?? 0;

                switch ($ratingFilter) {
                    case 'four_plus':
                        return $rating >= 4.0;
                    case 'three_plus':
                        return $rating >= 3.0;
                    case 'two_plus':
                        return $rating >= 2.0;
                    case 'one_plus':
                        return $rating >= 1.0;
                    default:
                        return true;
                }
            });
        }

        // Apply status filter (open/closed)
        if ($request->has('status')) {
            $statusFilter = $request->input('status');
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($statusFilter) {
                $isOpen = $location['google_places']['opening_hours']['open_now'] ?? false;

                if ($statusFilter === 'open') {
                    return $isOpen;
                } elseif ($statusFilter === 'closed') {
                    return !$isOpen;
                }

                return true;
            });
        }

        // Apply budget filter
        if ($request->has('budget')) {
            $budgetFilter = $request->input('budget');
            $budgetOptions = explode(',', $budgetFilter);

            $filteredLocations = array_filter($filteredLocations, function ($location) use ($budgetOptions) {
                $priceLevel = $location['google_places']['price_level'] ?? 0;

                foreach ($budgetOptions as $budget) {
                    switch (trim($budget)) {
                        case 'budget':
                            if ($priceLevel == 1) return true;
                            break;
                        case 'moderate':
                            if ($priceLevel == 2) return true;
                            break;
                        case 'expensive':
                            if ($priceLevel >= 3) return true;
                            break;
                    }
                }

                return false;
            });
        }

        // Apply family_friendly filter
        if ($request->has('family_friendly')) {
            $familyFriendlyFilter = $request->input('family_friendly') === 'true';
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($familyFriendlyFilter) {
                $isFamilyFriendly = $location['google_places']['family_friendly'] ?? false;
                return $familyFriendlyFilter ? $isFamilyFriendly : true;
            });
        }

        // Apply wheelchair_accessible filter
        if ($request->has('wheelchair_accessible')) {
            $wheelchairFilter = $request->input('wheelchair_accessible') === 'true';
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($wheelchairFilter) {
                $isWheelchairAccessible = $location['google_places']['wheelchair_accessible'] ?? false;
                return $wheelchairFilter ? $isWheelchairAccessible : true;
            });
        }

        // Apply pet_friendly filter
        if ($request->has('pet_friendly')) {
            $petFriendlyFilter = $request->input('pet_friendly') === 'true';
            $filteredLocations = array_filter($filteredLocations, function ($location) use ($petFriendlyFilter) {
                $isPetFriendly = $location['google_places']['pet_friendly'] ?? false;
                return $petFriendlyFilter ? $isPetFriendly : true;
            });
        }

        // Apply categories filter
        if ($request->has('categories')) {
            $categoriesFilter = $request->input('categories');
            $categoryIds = explode(',', $categoriesFilter);
            $categoryIds = array_map('intval', $categoryIds);

            $filteredLocations = array_filter($filteredLocations, function ($location) use ($categoryIds) {
                // For static data, we'll assume each location belongs to category ID 2 (Bites)
                // In real implementation, this would check the actual category field
                $locationCategoryId = $location['google_places']['category_id'] ?? 2;
                return in_array($locationCategoryId, $categoryIds);
            });
        }

        // Apply sorting
        if ($request->has('sort_by')) {
            $sortBy = $request->input('sort_by');

            if ($sortBy === 'relevance') {
                // Sort by rating (descending)
                usort($filteredLocations, function ($a, $b) {
                    $ratingA = $a['google_places']['rating'] ?? 0;
                    $ratingB = $b['google_places']['rating'] ?? 0;
                    return $ratingB <=> $ratingA;
                });
            } elseif ($sortBy === 'distance') {
                // For static data, we'll keep the original order
                // In real implementation, this would calculate distance
            }
        }

        return array_values($filteredLocations);
    }

    /**
     * Generate raw response text
     */
    private function generateRawResponse(string $query, array $locations, $latitude, $longitude): string
    {
        $count = count($locations);
        $response = "Here is a list of {$count} restaurant(s) in Dubai within 20km of the coordinates {$latitude}, {$longitude}. The search query '{$query}' returned the following results:\n\n## List of Restaurants\n\n";

        foreach ($locations as $index => $location) {
            $num = $index + 1;
            $name = $location['google_places']['name'] ?? 'Unknown';
            $rating = $location['google_places']['rating'] ?? 'N/A';
            $priceLevel = $location['google_places']['price_level'] ?? 'N/A';

            $response .= "{$num}. **{$name}**\n";
            $response .= "   - **Rating**: {$rating} stars\n";
            $response .= "   - **Price Level**: {$priceLevel}\n";
            $response .= "   - **Address**: " . ($location['formatted_address'] ?? 'N/A') . "\n\n";
        }

        $response .= "## Additional Information\n";
        $response .= "- All locations are within the specified search radius\n";
        $response .= "- Results are based on the search query: '{$query}'\n";
        $response .= "- Filters have been applied to the results";

        return $response;
    }
}
