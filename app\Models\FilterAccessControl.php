<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Model for managing filter access control settings
 * Controls which filters are locked for premium users only
 */
class FilterAccessControl extends Model
{
    use HasFactory;

    protected $table = 'filter_access_control';

    protected $fillable = [
        'working_hours_locked',
        'rating_locked',
        'family_friendly_locked',
        'budget_locked',
        'wheelchair_accessible_locked',
        'pet_friendly_locked',
        'sort_by_distance_locked',
    ];

    protected $casts = [
        'working_hours_locked' => 'boolean',
        'rating_locked' => 'boolean',
        'family_friendly_locked' => 'boolean',
        'budget_locked' => 'boolean',
        'wheelchair_accessible_locked' => 'boolean',
        'pet_friendly_locked' => 'boolean',
        'sort_by_distance_locked' => 'boolean',
    ];

    /**
     * Get the filter access control settings as an array
     */
    public function toFilterArray(): array
    {
        return [
            'working_hours_locked' => $this->working_hours_locked,
            'rating_locked' => $this->rating_locked,
            'family_friendly_locked' => $this->family_friendly_locked,
            'budget_locked' => $this->budget_locked,
            'wheelchair_accessible_locked' => $this->wheelchair_accessible_locked,
            'pet_friendly_locked' => $this->pet_friendly_locked,
            'sort_by_distance_locked' => $this->sort_by_distance_locked,
        ];
    }

    /**
     * Get list of locked filters
     */
    public function getLockedFilters(): \Illuminate\Support\Collection
    {
        $filters = collect();

        if ($this->working_hours_locked) $filters->push('Working Hours');
        if ($this->rating_locked) $filters->push('Rating');
        if ($this->family_friendly_locked) $filters->push('Family Friendly');
        if ($this->budget_locked) $filters->push('Budget');
        if ($this->wheelchair_accessible_locked) $filters->push('Wheelchair Accessible');
        if ($this->pet_friendly_locked) $filters->push('Pet Friendly');
        if ($this->sort_by_distance_locked) $filters->push('Sort by Distance');

        return $filters;
    }

    /**
     * Get list of unlocked filters
     */
    public function getUnlockedFilters(): \Illuminate\Support\Collection
    {
        $filters = collect();

        if (!$this->working_hours_locked) $filters->push('Working Hours');
        if (!$this->rating_locked) $filters->push('Rating');
        if (!$this->family_friendly_locked) $filters->push('Family Friendly');
        if (!$this->budget_locked) $filters->push('Budget');
        if (!$this->wheelchair_accessible_locked) $filters->push('Wheelchair Accessible');
        if (!$this->pet_friendly_locked) $filters->push('Pet Friendly');
        if (!$this->sort_by_distance_locked) $filters->push('Sort by Distance');

        return $filters;
    }

    /**
     * Check if all filters are locked
     */
    public function areAllFiltersLocked(): bool
    {
        return $this->working_hours_locked &&
               $this->rating_locked &&
               $this->family_friendly_locked &&
               $this->budget_locked &&
               $this->wheelchair_accessible_locked &&
               $this->pet_friendly_locked &&
               $this->sort_by_distance_locked;
    }

    /**
     * Check if all filters are unlocked
     */
    public function areAllFiltersUnlocked(): bool
    {
        return !$this->working_hours_locked &&
               !$this->rating_locked &&
               !$this->family_friendly_locked &&
               !$this->budget_locked &&
               !$this->wheelchair_accessible_locked &&
               !$this->pet_friendly_locked &&
               !$this->sort_by_distance_locked;
    }

    /**
     * Check if any filters are locked
     */
    public function hasLockedFilters(): bool
    {
        return $this->working_hours_locked ||
               $this->rating_locked ||
               $this->family_friendly_locked ||
               $this->budget_locked ||
               $this->wheelchair_accessible_locked ||
               $this->pet_friendly_locked ||
               $this->sort_by_distance_locked;
    }



    /**
     * Check if a specific filter is locked
     */
    public function isFilterLocked(string $filterType): bool
    {
        $field = $filterType . '_locked';

        if (!in_array($field, $this->fillable)) {
            return false;
        }

        return $this->{$field} ?? false;
    }

    /**
     * Get count of locked filters
     */
    public function getLockedFiltersCount(): int
    {
        return $this->getLockedFilters()->count();
    }

    /**
     * Get count of unlocked filters
     */
    public function getUnlockedFiltersCount(): int
    {
        return $this->getUnlockedFilters()->count();
    }
}
