<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ExportChatTrainingData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chat:export-training-data 
                            {--output= : Output file path (default: storage/app/chat_training_data.json)}
                            {--format=array : Output format (array|lines) - array for single JSON array, lines for JSONL format}
                            {--agent-id=1 : Agent ID to filter responses (default: 1 for General Rydo Agent)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export chat message pairs (user input + agent response) as JSON training data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting chat training data export...');

        $agentId = $this->option('agent-id');
        $format = $this->option('format');
        $outputPath = $this->option('output') ?? 'chat_training_data.json';

        // Validate format option
        if (!in_array($format, ['array', 'lines'])) {
            $this->error('Invalid format. Use "array" or "lines".');
            return 1;
        }

        try {
            // Execute the query to get message pairs
            $messagePairs = $this->getMessagePairs($agentId);

            if (empty($messagePairs)) {
                $this->warn('No message pairs found for agent ID: ' . $agentId);
                return 0;
            }

            $this->info("Found " . count($messagePairs) . " message pairs");

            // Format and save the data
            $this->saveTrainingData($messagePairs, $outputPath, $format);

            $this->info("Training data exported successfully to: {$outputPath}");
            $this->info("Total pairs exported: " . count($messagePairs));

        } catch (\Exception $e) {
            $this->error('Error exporting training data: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Get message pairs from the database
     */
    private function getMessagePairs($agentId)
    {
        $query = "
            WITH ordered_messages AS (
                SELECT 
                    id,
                    chat_session_id,
                    sender,
                    message_text,
                    agent_id,
                    created_at,
                    ROW_NUMBER() OVER (PARTITION BY chat_session_id ORDER BY created_at) as message_order
                FROM chat_messages
                WHERE sender IN ('user', 'agent')
                ORDER BY chat_session_id, created_at
            ),
            
            message_pairs AS (
                SELECT 
                    user_msg.chat_session_id,
                    user_msg.message_text as user_input,
                    agent_msg.message_text as agent_response,
                    user_msg.created_at as user_timestamp,
                    agent_msg.created_at as agent_timestamp,
                    user_msg.message_order as user_order,
                    agent_msg.message_order as agent_order
                FROM ordered_messages user_msg
                JOIN ordered_messages agent_msg 
                    ON user_msg.chat_session_id = agent_msg.chat_session_id
                    AND user_msg.message_order + 1 = agent_msg.message_order
                WHERE user_msg.sender = 'user'
                    AND agent_msg.sender = 'agent'
                    AND agent_msg.agent_id = ?
                    AND user_msg.message_text IS NOT NULL
                    AND agent_msg.message_text IS NOT NULL
                    AND TRIM(user_msg.message_text) != ''
                    AND TRIM(agent_msg.message_text) != ''
            )
            
            SELECT 
                user_input,
                agent_response,
                user_timestamp
            FROM message_pairs
            ORDER BY user_timestamp
        ";

        return DB::select($query, [$agentId]);
    }

    /**
     * Save training data to file
     */
    private function saveTrainingData($messagePairs, $outputPath, $format)
    {
        if ($format === 'array') {
            // Single JSON array format
            $data = collect($messagePairs)->map(function ($pair) {
                return [
                    'input' => $pair->user_input,
                    'response' => $pair->agent_response
                ];
            })->toArray();

            $jsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            // JSONL format (one JSON object per line)
            $lines = collect($messagePairs)->map(function ($pair) {
                return json_encode([
                    'input' => $pair->user_input,
                    'response' => $pair->agent_response
                ], JSON_UNESCAPED_UNICODE);
            })->toArray();

            $jsonContent = implode("\n", $lines);
        }

        // Save to storage
        Storage::put($outputPath, $jsonContent);
    }

    /**
     * Get message pairs and return as array (for API usage)
     */
    public function getMessagePairsAsArray($agentId = 1)
    {
        $messagePairs = $this->getMessagePairs($agentId);

        return collect($messagePairs)->map(function ($pair) {
            return [
                'input' => $pair->user_input,
                'response' => $pair->agent_response
            ];
        })->toArray();
    }
}
