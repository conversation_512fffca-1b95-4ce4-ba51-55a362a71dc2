<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NearbyPlacesRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'lat' => 'required|numeric|between:-90,90',
            'lng' => 'required|numeric|between:-180,180',
            'agent_ids' => 'nullable|string', // Comma-separated agent IDs
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'lat.required' => 'The latitude is required.',
            'lat.numeric' => 'The latitude must be a number.',
            'lat.between' => 'The latitude must be between -90 and 90.',
            'lng.required' => 'The longitude is required.',
            'lng.numeric' => 'The longitude must be a number.',
            'lng.between' => 'The longitude must be between -180 and 180.',
        ];
    }
}
