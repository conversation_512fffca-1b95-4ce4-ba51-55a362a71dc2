# RevenueCat Integration Documentation

This document outlines the RevenueCat webhook integration and subscription status endpoints implemented in the Rydo Laravel backend.

## Overview

The integration provides:
1. **Webhook Integration**: Handles RevenueCat webhook events for subscription lifecycle management
2. **Enhanced Subscription Status API**: Returns subscription status with entitlements and RevenueCat-specific data
3. **Database Integration**: Stores RevenueCat data alongside existing subscription information

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# RevenueCat Configuration
REVENUECAT_API_KEY=your_revenuecat_api_key
REVENUECAT_WEBHOOK_SECRET=your_webhook_secret
REVENUECAT_PUBLIC_API_KEY=your_public_api_key
```

### Database Migrations

Run the migrations to add RevenueCat support:

```bash
php artisan migrate
```

This will create:
- Additional columns in the `subscriptions` table for RevenueCat data
- `revenuecat_webhook_events` table for webhook event logging

## Webhook Integration

### Endpoint

**URL**: `POST /api/revenuecat/webhook`

**Features**:
- Signature verification using HMAC SHA256
- Event logging for debugging and audit purposes
- Automatic user creation for new RevenueCat customers
- Database transaction safety
- Comprehensive error handling

### Supported Events

| Event Type | Description | Action |
|------------|-------------|---------|
| `INITIAL_PURCHASE` | New subscription created | Creates/updates subscription with active status |
| `RENEWAL` | Subscription renewed | Updates subscription with new expiration date |
| `CANCELLATION` | Subscription cancelled | Marks subscription as cancelled |
| `EXPIRATION` | Subscription expired | Marks subscription as expired |
| `BILLING_ISSUE` | Payment failed | Logs billing issue (subscription remains active) |
| `PRODUCT_CHANGE` | Plan changed | Updates subscription with new product details |

### Webhook Payload Example

```json
{
  "api_version": "1.0",
  "event": {
    "type": "INITIAL_PURCHASE",
    "app_user_id": "user123",
    "customer_info": {
      "customer_id": "rc_customer_123"
    },
    "product_id": "rydo_plus_monthly",
    "entitlement_id": "premium",
    "store": "app_store",
    "environment": "PRODUCTION",
    "purchased_at_ms": 1640995200000,
    "expiration_at_ms": 1643673600000,
    "is_trial_period": false
  }
}
```

## Subscription Status API

### Endpoint

**URL**: `GET /api/subscriptions/status`
**Authentication**: Required (Sanctum)

### Response Format

```json
{
  "is_subscribed": true,
  "subscription_status": "active",
  "current_plan": "RYDO_PLUS",
  "expires_at": "2024-02-01T00:00:00.000000Z",
  "entitlements": {
    "unlimited_chat": true,
    "premium_agents": true,
    "advanced_recommendations": true,
    "priority_support": true,
    "ad_free_experience": true,
    "offline_maps": true,
    "export_data": true,
    "monthly_points": 1000
  },
  "has_active_subscription": true,
  "subscription": {
    "id": 1,
    "status": "active",
    "start_date": "2024-01-01T00:00:00.000000Z",
    "end_date": "Feb 01 2024",
    "auto_renewal": true,
    "platform_type": null,
    "product_id": null,
    "transaction_id": null,
    "days_until_renewal": 30
  }
}
```

### Subscription Status Values

- `none`: No subscription
- `active`: Active subscription
- `trial`: Trial period
- `canceled`: Cancelled subscription
- `expired`: Expired subscription

### Entitlements

The API returns feature entitlements based on the subscription plan:

#### Free Tier
- `monthly_points`: 100
- All other features: `false`

#### Basic Tier
- `unlimited_chat`: `true`
- `advanced_recommendations`: `true`
- `ad_free_experience`: `true`
- `monthly_points`: 500

#### Rydo Plus Tier
- All features: `true`
- `monthly_points`: 1000

## Product ID Mapping

Configure your RevenueCat product IDs in `RevenueCatWebhookService::mapProductIdToPlan()`:

```php
return match ($productId) {
    'rydo_plus_monthly', 'rydo_plus_yearly', 'com.rydo.plus.monthly', 'com.rydo.plus.yearly' => SubscriptionPlan::RYDO_PLUS,
    'rydo_basic_monthly', 'rydo_basic_yearly' => SubscriptionPlan::BASIC,
    default => SubscriptionPlan::RYDO_PLUS,
};
```

## Testing

Run the test suite:

```bash
php artisan test tests/Feature/RevenueCatWebhookTest.php
```

### Manual Testing

1. **Test webhook signature verification**:
   ```bash
   curl -X POST http://your-app.com/api/revenuecat/webhook \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
   ```
   Should return 400 with "Invalid signature"

2. **Test subscription status**:
   ```bash
   curl -X GET http://your-app.com/api/subscriptions/status \
     -H "Authorization: Bearer your_token"
   ```

## Security Considerations

1. **Webhook Signature Verification**: All webhooks are verified using HMAC SHA256
2. **CSRF Protection**: Webhook endpoint is excluded from CSRF protection
3. **Rate Limiting**: Consider implementing rate limiting for the webhook endpoint
4. **Environment Validation**: Webhooks include environment (SANDBOX/PRODUCTION) validation

## Monitoring and Debugging

### Webhook Event Logs

All webhook events are logged in the `revenuecat_webhook_events` table with:
- Full webhook payload
- Processing status (pending/processed/failed)
- Error messages for failed events
- Associated user and subscription IDs

### Application Logs

Check Laravel logs for:
- Webhook signature verification failures
- Processing errors
- User creation events

## Troubleshooting

### Common Issues

1. **Table 'revenue_cat_webhook_events' doesn't exist**
   - Ensure migrations have been run on production: `php artisan migrate --force`
   - Check if auto-migration is enabled in Laravel Cloud dashboard
   - Verify the migration files are deployed to production

2. **Webhook signature verification fails**
   - Verify `REVENUECAT_WEBHOOK_SECRET` is correctly set
   - Check RevenueCat dashboard webhook configuration

2. **User not found for webhook**
   - Ensure `app_user_id` in RevenueCat matches your user ID or email
   - Check user creation logic in `RevenueCatWebhookService::findOrCreateUser()`

3. **Subscription not updating**
   - Check webhook event logs in database
   - Verify product ID mapping in `mapProductIdToPlan()`

### Debug Mode

Enable detailed logging by setting `LOG_LEVEL=debug` in your `.env` file.
