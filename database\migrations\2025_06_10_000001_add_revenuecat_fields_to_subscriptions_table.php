<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // RevenueCat specific fields
            $table->string('revenuecat_customer_id')->nullable()->after('original_transaction_id');
            $table->string('revenuecat_original_app_user_id')->nullable()->after('revenuecat_customer_id');
            $table->string('revenuecat_entitlement_id')->nullable()->after('revenuecat_original_app_user_id');
            $table->string('revenuecat_product_identifier')->nullable()->after('revenuecat_entitlement_id');
            $table->timestamp('revenuecat_purchased_at')->nullable()->after('revenuecat_product_identifier');
            $table->timestamp('revenuecat_expires_at')->nullable()->after('revenuecat_purchased_at');
            $table->string('revenuecat_store')->nullable()->after('revenuecat_expires_at'); // app_store, play_store, stripe, etc.
            $table->string('revenuecat_environment')->nullable()->after('revenuecat_store'); // SANDBOX, PRODUCTION
            $table->boolean('revenuecat_is_trial_period')->default(false)->after('revenuecat_environment');
            $table->json('revenuecat_webhook_data')->nullable()->after('revenuecat_is_trial_period'); // Store full webhook payload for debugging
            
            // Add indexes for better query performance
            $table->index('revenuecat_customer_id');
            $table->index('revenuecat_original_app_user_id');
            $table->index(['revenuecat_store', 'revenuecat_environment']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropIndex(['revenuecat_store', 'revenuecat_environment']);
            $table->dropIndex(['revenuecat_original_app_user_id']);
            $table->dropIndex(['revenuecat_customer_id']);
            
            $table->dropColumn([
                'revenuecat_customer_id',
                'revenuecat_original_app_user_id',
                'revenuecat_entitlement_id',
                'revenuecat_product_identifier',
                'revenuecat_purchased_at',
                'revenuecat_expires_at',
                'revenuecat_store',
                'revenuecat_environment',
                'revenuecat_is_trial_period',
                'revenuecat_webhook_data',
            ]);
        });
    }
};
