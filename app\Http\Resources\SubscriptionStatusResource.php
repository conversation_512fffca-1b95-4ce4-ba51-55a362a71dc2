<?php

namespace App\Http\Resources;

use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class SubscriptionStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $this->resource;
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        $lastSubscription = Subscription::getLastSubscription($user->id);

        $data = [
            'is_subscribed' => $currentSubscription !== null,
            'has_active_subscription' => $currentSubscription !== null,
            'subscription_status' => $this->getSubscriptionStatus($currentSubscription),
            'current_plan' => $currentSubscription?->plan?->value,
            'expires_at' => $this->getExpirationDate($currentSubscription),
            'entitlements' => $this->getEntitlements($currentSubscription),
            'subscription' => $currentSubscription ? SubscriptionResource::make($currentSubscription) : null,
        ];

        // Add subscription history for reactivation
        if (!$currentSubscription && $lastSubscription) {
            $data['subscription_history'] = [
                'has_previous_subscription' => true,
                'can_reactivate' => $lastSubscription->canReactivate(),
                'last_subscription' => [
                    'plan' => $lastSubscription->plan->value,
                    'status' => $lastSubscription->status->value,
                    'ended_at' => $lastSubscription->end_date->toISOString(),
                    'was_auto_renewal' => $lastSubscription->auto_renewal ?? true,
                    'was_cancelled' => $lastSubscription->isCancelled(),
                ]
            ];
        }

        // Add cancellation info for cancelled subscriptions
        if ($currentSubscription && $currentSubscription->isCancelled()) {
            $data['cancellation_info'] = [
                'is_cancelled' => true,
                'cancellation_date' => $currentSubscription->cancellation_date->toISOString(),
                'days_until_access_ends' => $currentSubscription->getDaysUntilAccessEnds(),
            ];
        }

        // Add available plan info if no active subscription
        if (!$currentSubscription) {
            $rydoPlusPlan = Plan::where('code', 'RYDO_PLUS')->where('is_active', true)->first();
            if ($rydoPlusPlan) {
                $data['available_plan'] = SubscriptionPlanResource::make($rydoPlusPlan);
            }
        }

        return $data;
    }

    /**
     * Get subscription status string
     *
     * @param Subscription|null $subscription
     * @return string
     */
    private function getSubscriptionStatus(?Subscription $subscription): string
    {
        if (!$subscription) {
            return 'none';
        }

        if ($subscription->isInTrialPeriod()) {
            return 'trial';
        }

        return $subscription->status->value;
    }

    /**
     * Get expiration date in ISO 8601 format
     *
     * @param Subscription|null $subscription
     * @return string|null
     */
    private function getExpirationDate(?Subscription $subscription): ?string
    {
        if (!$subscription) {
            return null;
        }

        $expirationDate = $subscription->getEffectiveExpirationDate();
        return $expirationDate?->toISOString();
    }

    /**
     * Get user entitlements based on subscription
     *
     * @param Subscription|null $subscription
     * @return array
     */
    private function getEntitlements(?Subscription $subscription): array
    {
        if (!$subscription || !$subscription->isActive()) {
            return $this->getFreeEntitlements();
        }

        return match ($subscription->plan) {
            \App\Enums\SubscriptionPlan::RYDO_PLUS => $this->getRydoPlusEntitlements(),
            \App\Enums\SubscriptionPlan::BASIC => $this->getBasicEntitlements(),
            default => $this->getFreeEntitlements(),
        };
    }

    /**
     * Get free tier entitlements
     *
     * @return array
     */
    private function getFreeEntitlements(): array
    {
        return [
            'unlimited_chat' => false,
            'premium_agents' => false,
            'advanced_recommendations' => false,
            'priority_support' => false,
            'ad_free_experience' => false,
            'offline_maps' => false,
            'export_data' => false,
            'monthly_points' => 100,
        ];
    }

    /**
     * Get basic tier entitlements
     *
     * @return array
     */
    private function getBasicEntitlements(): array
    {
        return [
            'unlimited_chat' => true,
            'premium_agents' => false,
            'advanced_recommendations' => true,
            'priority_support' => false,
            'ad_free_experience' => true,
            'offline_maps' => false,
            'export_data' => false,
            'monthly_points' => 500,
        ];
    }

    /**
     * Get Rydo Plus entitlements
     *
     * @return array
     */
    private function getRydoPlusEntitlements(): array
    {
        return [
            'unlimited_chat' => true,
            'premium_agents' => true,
            'advanced_recommendations' => true,
            'priority_support' => true,
            'ad_free_experience' => true,
            'offline_maps' => true,
            'export_data' => true,
            'monthly_points' => 1000,
        ];
    }

}
