<?php

namespace App\Http\Resources;

use App\Models\Subscription;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class SubscriptionStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $this->resource;
        $currentSubscription = Subscription::getCurrentSubscription($user->id);

        return [
            'status' => $this->getSubscriptionStatus($currentSubscription),
            'product_id' => $currentSubscription?->product_id,
            'expiration_date' => $this->getFormattedExpirationDate($currentSubscription),
            'price' => $this->getFormattedPrice($currentSubscription),
        ];
    }

    /**
     * Get subscription status string
     *
     * @param Subscription|null $subscription
     * @return string
     */
    private function getSubscriptionStatus(?Subscription $subscription): string
    {
        if (!$subscription) {
            return 'none';
        }

        if ($subscription->isInTrialPeriod()) {
            return 'trial';
        }

        return $subscription->status->value;
    }

    /**
     * Get expiration date formatted as "Apr 12 2025"
     *
     * @param Subscription|null $subscription
     * @return string|null
     */
    private function getFormattedExpirationDate(?Subscription $subscription): ?string
    {
        if (!$subscription) {
            return null;
        }

        $expirationDate = $subscription->getEffectiveExpirationDate();
        return $expirationDate?->format('M j Y');
    }

    /**
     * Get formatted price with currency
     *
     * @param Subscription|null $subscription
     * @return string|null
     */
    private function getFormattedPrice(?Subscription $subscription): ?string
    {
        if (!$subscription) {
            return null;
        }

        $planDetails = $subscription->planDetails;
        if (!$planDetails) {
            return null;
        }

        return $planDetails->currency . ' ' . number_format($planDetails->price, 2);
    }
}
