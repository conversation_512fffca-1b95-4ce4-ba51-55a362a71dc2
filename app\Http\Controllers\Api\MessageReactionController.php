<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\MessageReaction;
use App\Models\MessageReport;
use App\Enums\ReactionType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class MessageReactionController extends Controller
{
    /**
     * Store a message reaction (like, dislike, copy)
     */
    public function storeReaction(Request $request): JsonResponse
    {
        // Log all incoming data before validation
        \Log::info('Raw message reaction request:', [
            'all_data' => $request->all(),
            'chat_message_id' => $request->chat_message_id,
            'chat_message_id_type' => gettype($request->chat_message_id),
            'headers' => $request->headers->all(),
        ]);

        $request->validate([
            'chat_message_id' => 'required|integer|exists:chat_messages,id',
            'reaction' => 'required|in:like,dislike,copy',
            'agent_id' => 'nullable|integer|exists:agents,id',
            'user_id' => 'nullable|integer',
            'message_content' => 'nullable|string',
            'timestamp' => 'nullable|date',
        ]);

        $user = $request->user();

        // Check if message exists first
        $chatMessage = ChatMessage::find($request->chat_message_id);
        if (!$chatMessage) {
            \Log::error('Message reaction failed - message not found:', [
                'chat_message_id' => $request->chat_message_id,
                'user_id' => $user->id,
            ]);

            return response()->json([
                'message' => 'The selected chat message id is not found',
                'success' => false,
            ], 422);
        }

        // Log the incoming request for debugging
        \Log::info('Message reaction request data:', [
            'chat_message_id' => $request->chat_message_id,
            'reaction' => $request->reaction,
            'agent_id' => $request->agent_id,
            'user_id' => $request->user_id,
            'message_content' => $request->message_content ? substr($request->message_content, 0, 100) . '...' : null,
            'timestamp' => $request->timestamp,
            'authenticated_user_id' => $user->id,
            'message_exists' => true,
            'message_agent_id' => $chatMessage->agent_id,
            'message_chat_session_id' => $chatMessage->chat_session_id,
        ]);

        // Check if user already reacted to this message
        $existingReaction = MessageReaction::where([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
        ])->first();

        if ($existingReaction) {
            // Update existing reaction
            $existingReaction->update([
                'reaction' => $request->reaction,
                'copied_at' => $request->reaction === 'copy' ? now() : null,
            ]);
            $reaction = $existingReaction;
        } else {
            // Create new reaction
            $reaction = MessageReaction::create([
                'user_id' => $user->id,
                'chat_message_id' => $chatMessage->id,
                'agent_id' => $chatMessage->agent_id,
                'reaction' => $request->reaction,
                'copied_at' => $request->reaction === 'copy' ? now() : null,
            ]);
        }

        return response()->json([
            'message' => 'Reaction recorded successfully',
            'success' => true,
            'data' => [
                'reaction' => $reaction->reaction,
                'message_id' => $chatMessage->id,
            ]
        ]);
    }

    /**
     * Store a message report
     */
    public function storeReport(Request $request): JsonResponse
    {
        $request->validate([
            'chat_message_id' => 'nullable|integer',
            'chat_session_id' => 'nullable|integer',
            'report_reason' => 'required|string|max:1000',
        ]);

        $user = $request->user();
        $chatMessageId = $request->chat_message_id;
        $chatSessionId = $request->chat_session_id;
        $agentId = null;

        // Log the incoming request for debugging
        \Log::info('Report request data:', [
            'chat_message_id' => $chatMessageId,
            'chat_session_id' => $chatSessionId,
            'user_id' => $user->id,
            'all_request_data' => $request->all(),
        ]);

        // Check if message exists when provided
        if ($chatMessageId) {
            $messageExists = ChatMessage::where('id', $chatMessageId)->exists();
            \Log::info('Message exists check:', [
                'message_id' => $chatMessageId,
                'exists' => $messageExists,
            ]);

            if (!$messageExists) {
                return response()->json([
                    'message' => 'The selected message does not exist',
                    'success' => false,
                ], 422);
            }
        }

        // Check if session exists when provided
        if ($chatSessionId) {
            $sessionExists = ChatSession::where('id', $chatSessionId)->exists();
            \Log::info('Session exists check:', [
                'session_id' => $chatSessionId,
                'exists' => $sessionExists,
            ]);

            if (!$sessionExists) {
                return response()->json([
                    'message' => 'The selected chat session does not exist',
                    'success' => false,
                ], 422);
            }
        }

        // If message ID is provided, get the agent ID and session ID from the message
        if ($chatMessageId) {
            $chatMessage = ChatMessage::findOrFail($chatMessageId);
            $agentId = $chatMessage->agent_id;
            $chatSessionId = $chatMessage->chat_session_id;
        } elseif ($chatSessionId) {
            // For general reports, get agent ID from the session
            $chatSession = ChatSession::findOrFail($chatSessionId);
            $agentId = $chatSession->agent_id;
        } else {
            return response()->json([
                'message' => 'Either chat_message_id or chat_session_id is required',
                'success' => false,
            ], 422);
        }

        // Ensure agent_id is not null
        if (!$agentId) {
            return response()->json([
                'message' => 'Unable to determine agent for this report',
                'success' => false,
            ], 422);
        }

        // Check if user already reported this message (if message_id provided)
        if ($chatMessageId) {
            $existingReport = MessageReport::where([
                'user_id' => $user->id,
                'chat_message_id' => $chatMessageId,
            ])->first();

            if ($existingReport) {
                return response()->json([
                    'message' => 'You have already reported this message',
                    'success' => false,
                ], 422);
            }
        }

        // Create the report
        MessageReport::create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessageId,
            'chat_session_id' => $chatSessionId,
            'agent_id' => $agentId,
            'report_reason' => $request->report_reason,
        ]);

        return response()->json([
            'message' => 'Report submitted successfully. Thank you for your feedback.',
            'success' => true,
        ]);
    }

    /**
     * Remove a reaction (for when user wants to undo their reaction)
     */
    public function removeReaction(Request $request): JsonResponse
    {
        $request->validate([
            'chat_message_id' => 'required|exists:chat_messages,id',
        ]);

        $user = $request->user();

        $reaction = MessageReaction::where([
            'user_id' => $user->id,
            'chat_message_id' => $request->chat_message_id,
        ])->first();

        if (!$reaction) {
            return response()->json([
                'message' => 'No reaction found to remove',
                'success' => false,
            ], 404);
        }

        $reaction->delete();

        return response()->json([
            'message' => 'Reaction removed successfully',
            'success' => true,
        ]);
    }

    /**
     * Get user's reactions for multiple messages
     */
    public function getUserReactions(Request $request): JsonResponse
    {
        $request->validate([
            'message_ids' => 'required|array',
            'message_ids.*' => 'integer|exists:chat_messages,id',
        ]);

        $user = $request->user();
        $messageIds = $request->message_ids;

        // Get user's reactions for the specified messages
        $reactions = MessageReaction::where('user_id', $user->id)
            ->whereIn('chat_message_id', $messageIds)
            ->get(['chat_message_id', 'reaction'])
            ->keyBy('chat_message_id');

        // Format response
        $userReactions = [];
        foreach ($messageIds as $messageId) {
            $userReactions[$messageId] = $reactions->has($messageId)
                ? $reactions[$messageId]->reaction->value
                : null;
        }

        return response()->json([
            'message' => 'User reactions retrieved successfully',
            'success' => true,
            'data' => $userReactions,
        ]);
    }
}
