<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add website and formatted_phone_number fields to places table
     */
    public function up(): void
    {
        Schema::table('places', function (Blueprint $table) {
            $table->string('website')->nullable()->after('opening_hours');
            $table->string('phone_number')->nullable()->after('website');
            $table->string('cid')->nullable()->after('phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('places', function (Blueprint $table) {
            $table->dropColumn(['website', 'phone_number', 'cid']);
        });
    }
};
