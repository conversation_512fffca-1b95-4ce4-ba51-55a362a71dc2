<?php

namespace App\Filament\Resources\FilterAccessControlResource\Pages;

use App\Filament\Resources\FilterAccessControlResource;
use App\Models\FilterAccessControl;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListFilterAccessControls extends ListRecords
{
    protected static string $resource = FilterAccessControlResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn () => FilterAccessControl::count() === 0),

            Actions\Action::make('lockAllFilters')
                ->label('Lock All Filters')
                ->icon('heroicon-o-lock-closed')
                ->color('danger')
                ->requiresConfirmation()
                ->modalDescription('This will lock all filters for premium users only.')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => true,
                        'rating_locked' => true,
                        'family_friendly_locked' => true,
                        'budget_locked' => true,
                        'wheelchair_accessible_locked' => true,
                        'pet_friendly_locked' => true,
                        'sort_by_distance_locked' => true,
                    ]);

                    Notification::make()
                        ->success()
                        ->title('All Filters Locked')
                        ->body('All filters are now locked for premium users only.')
                        ->send();

                    $this->redirect(static::getUrl());
                }),

            Actions\Action::make('unlockAllFilters')
                ->label('Unlock All Filters')
                ->icon('heroicon-o-lock-open')
                ->color('success')
                ->requiresConfirmation()
                ->modalDescription('This will unlock all filters for all users.')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => false,
                        'rating_locked' => false,
                        'family_friendly_locked' => false,
                        'budget_locked' => false,
                        'wheelchair_accessible_locked' => false,
                        'pet_friendly_locked' => false,
                        'sort_by_distance_locked' => false,
                    ]);

                    Notification::make()
                        ->success()
                        ->title('All Filters Unlocked')
                        ->body('All filters are now available for all users.')
                        ->send();

                    $this->redirect(static::getUrl());
                }),

            Actions\Action::make('resetToDefaults')
                ->label('Reset to Defaults')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->requiresConfirmation()
                ->modalDescription('This will reset all filter settings to their default values (all unlocked).')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => false,
                        'rating_locked' => false,
                        'family_friendly_locked' => false,
                        'budget_locked' => false,
                        'wheelchair_accessible_locked' => false,
                        'pet_friendly_locked' => false,
                        'sort_by_distance_locked' => false,
                    ]);

                    Notification::make()
                        ->success()
                        ->title('Settings Reset')
                        ->body('All filter settings have been reset to defaults.')
                        ->send();

                    $this->redirect(static::getUrl());
                }),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            FilterAccessControlResource\Widgets\FilterStatusOverview::class,
        ];
    }

    public function mount(): void
    {
        parent::mount();

        // Ensure at least one record exists
        if (FilterAccessControl::count() === 0) {
            FilterAccessControl::create([]);
        }
    }
}
