<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('revenuecat_webhook_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_type'); // INITIAL_PURCHASE, RENEWAL, CANCELLATION, etc.
            $table->string('revenuecat_customer_id')->nullable();
            $table->string('revenuecat_original_app_user_id')->nullable();
            $table->string('product_id')->nullable();
            $table->string('entitlement_id')->nullable();
            $table->string('store')->nullable(); // app_store, play_store, stripe, etc.
            $table->string('environment')->nullable(); // SANDBOX, PRODUCTION
            $table->json('webhook_payload'); // Store the full webhook payload
            $table->string('processing_status')->default('pending'); // pending, processed, failed
            $table->text('processing_error')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();

            // Add indexes for better query performance
            $table->index('event_type');
            $table->index('revenuecat_customer_id');
            $table->index('revenuecat_original_app_user_id');
            $table->index('processing_status');
            $table->index(['store', 'environment']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('revenuecat_webhook_events');
    }
};
