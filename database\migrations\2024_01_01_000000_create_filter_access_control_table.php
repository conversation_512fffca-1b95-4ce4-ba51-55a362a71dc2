<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('filter_access_control', function (Blueprint $table) {
            $table->id();
            
            // Filter access control settings
            $table->boolean('working_hours_locked')->default(false)->comment('Lock working hours filter for premium users');
            $table->boolean('rating_locked')->default(false)->comment('Lock rating filter for premium users');
            $table->boolean('family_friendly_locked')->default(false)->comment('Lock family friendly filter for premium users');
            $table->boolean('budget_locked')->default(false)->comment('Lock budget filter for premium users');
            $table->boolean('wheelchair_accessible_locked')->default(false)->comment('Lock wheelchair accessible filter for premium users');
            $table->boolean('pet_friendly_locked')->default(false)->comment('Lock pet friendly filter for premium users');
            $table->boolean('sort_by_distance_locked')->default(false)->comment('Lock sort by distance filter for premium users');
            
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index(['working_hours_locked']);
            $table->index(['rating_locked']);
            $table->index(['family_friendly_locked']);
            $table->index(['budget_locked']);
            $table->index(['wheelchair_accessible_locked']);
            $table->index(['pet_friendly_locked']);
            $table->index(['sort_by_distance_locked']);
        });
        
        // Insert default settings
        DB::table('filter_access_control')->insert([
            'working_hours_locked' => false,
            'rating_locked' => false,
            'family_friendly_locked' => false,
            'budget_locked' => false,
            'wheelchair_accessible_locked' => false,
            'pet_friendly_locked' => false,
            'sort_by_distance_locked' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('filter_access_control');
    }
};
