<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RevenueCatWebhookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by signature verification
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'api_version' => 'required|string',
            'event' => 'required|array',
            'event.type' => 'required|string|in:INITIAL_PURCHASE,RENEWAL,CANCELLATION,EXPIRATION,BILLING_ISSUE,PRODUCT_CHANGE',
            'event.app_user_id' => 'required|string',
            'event.customer_info' => 'required|array',
            'event.customer_info.customer_id' => 'required|string',
            'event.product_id' => 'nullable|string',
            'event.entitlement_id' => 'nullable|string',
            'event.store' => 'nullable|string',
            'event.environment' => 'required|string|in:SANDBOX,PRODUCTION',
            'event.purchased_at_ms' => 'nullable|integer',
            'event.expiration_at_ms' => 'nullable|integer',
            'event.is_trial_period' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'event.type.in' => 'The event type must be one of: INITIAL_PURCHASE, RENEWAL, CANCELLATION, EXPIRATION, BILLING_ISSUE, PRODUCT_CHANGE',
            'event.environment.in' => 'The environment must be either SANDBOX or PRODUCTION',
        ];
    }
}
