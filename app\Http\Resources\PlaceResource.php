<?php

namespace App\Http\Resources;

use App\Actions\Places\CalculateEstimatedTimeAction;
use App\Actions\Places\CheckPlaceIsFavoriteAction;
use App\Actions\Places\GetPlaceFavoriteCountAction;
use App\Actions\Places\GetPlaceFavoriteTypeAction;
use App\Models\AdditionalInfo;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlaceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $request->user();

        $isOpen = collect([true, false])->random();

        $data = [
            'id' => $this->id,
            'title' => $this->name,
            'description' => $this->description,
            'brief_description' => $this->brief_description,
            'rating' => number_format($this->rating, 1),
            'address' => $this->address,
            'favorite_count' => app(GetPlaceFavoriteCountAction::class)->execute($this->resource),
            'is_favorite' => app(CheckPlaceIsFavoriteAction::class)->execute($user, $this->resource),
            'favorite_type' => app(GetPlaceFavoriteTypeAction::class)->execute($user, $this->resource),
            'images' => $this->images, // Using the accessor we defined
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'cid' => '9716004097539422534', // Temporary fixed CID
            'agent_id' => $this->agent_id,
            'agent_image' => $this->agent?->agent_avatar,
            'additional_info' => $this->agent?->additionalInfo()
                ->where('is_active', true)
                ->get()
                ->pluck('name'),
            'estimated_time' => app(CalculateEstimatedTimeAction::class)->execute(
                $request->header( 'X-Lat'),
                $request->query('X-Lng'),
                $this->resource
            ),
            'category_name' => $this->whenLoaded('category',fn($category) => $category->name),
            'is_open' => $isOpen,
            'status_time' => $isOpen ? 'Closes at 11:00 PM' : 'Open at 11:00 AM',
            // Filter fields
            'price_level' => $this->price_level,
            'family_friendly' => $this->family_friendly,
            'wheelchair_accessible' => $this->wheelchair_accessible,
            'pet_friendly' => $this->pet_friendly,
        ];

        $rawContactInfo = [
            [
                'title' => 'Website',
                'value' => $this->website,
                'type' => 'website',
                'icon' => 'https://fls-9e65c950-1610-4e89-9a69-612c327ac9d8.laravel.cloud/293/01JV1VPKWGXQFRQTS58GBZBXWB.png', // we can make it as collection media
            ],
            [
                'title' => 'Phone Number',
                'type' => 'phone_number',
                'value' => $this->phone_number,
                'icon' => 'https://fls-9e65c950-1610-4e89-9a69-612c327ac9d8.laravel.cloud/292/01JV1VPHDQ5D4TZDCAYH5QNC6F.png', //we can make it as collection media
            ],
        ];

        $contactInfo = collect($rawContactInfo)
            ->filter(fn($item) => !empty($item['value']))
            ->values();

        if ($contactInfo->isNotEmpty()) {
            $data['contact_info'] = $contactInfo->all();
        }

        return $data;
    }
}
