<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add filter fields to places table for advanced filtering functionality
     */
    public function up(): void
    {
        Schema::table('places', function (Blueprint $table) {
            // Price level for budget filtering (1=budget, 2=moderate, 3=expensive)
            $table->tinyInteger('price_level')->nullable()->after('rating');
            
            // Family friendly flag
            $table->boolean('family_friendly')->nullable()->after('price_level');
            
            // Wheelchair accessible flag
            $table->boolean('wheelchair_accessible')->nullable()->after('family_friendly');
            
            // Pet friendly flag
            $table->boolean('pet_friendly')->nullable()->after('wheelchair_accessible');
            
            // Add indexes for better query performance
            $table->index('price_level');
            $table->index('family_friendly');
            $table->index('wheelchair_accessible');
            $table->index('pet_friendly');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('places', function (Blueprint $table) {
            $table->dropIndex(['price_level']);
            $table->dropIndex(['family_friendly']);
            $table->dropIndex(['wheelchair_accessible']);
            $table->dropIndex(['pet_friendly']);
            
            $table->dropColumn([
                'price_level',
                'family_friendly',
                'wheelchair_accessible',
                'pet_friendly'
            ]);
        });
    }
};
