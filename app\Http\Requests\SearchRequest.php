<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'prompt' => 'required|string|max:255',
            'sort_by' => 'nullable|string|in:distance,relevance',
            'status' => 'nullable|string|in:open,closed',
            'rating' => 'nullable|string|in:four_plus,three_plus,two_plus,one_plus',
            'family_friendly' => 'nullable|string|in:true,false',
            'budget' => 'nullable|string',
            'wheelchair_accessible' => 'nullable|string|in:true,false',
            'pet_friendly' => 'nullable|string|in:true,false',
            'categories' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'prompt.required' => 'Search prompt is required.',
            'prompt.max' => 'Search prompt must not exceed 255 characters.',
            'sort_by.in' => 'Sort by must be either distance or relevance.',
            'status.in' => 'Status must be either open or closed.',
            'rating.in' => 'Rating must be one of: four_plus, three_plus, two_plus, one_plus.',
            'family_friendly.in' => 'Family friendly must be either true or false.',
            'wheelchair_accessible.in' => 'Wheelchair accessible must be either true or false.',
            'pet_friendly.in' => 'Pet friendly must be either true or false.',
        ];
    }
}
