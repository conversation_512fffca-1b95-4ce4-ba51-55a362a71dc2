<?php

namespace App\Filament\Resources\UserAgentPreferenceResource\Pages;

use App\Filament\Resources\UserAgentPreferenceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserAgentPreferences extends ListRecords
{
    protected static string $resource = UserAgentPreferenceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
