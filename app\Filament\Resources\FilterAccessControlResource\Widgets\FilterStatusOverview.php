<?php

namespace App\Filament\Resources\FilterAccessControlResource\Widgets;

use App\Models\FilterAccessControl;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class FilterStatusOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $record = FilterAccessControl::first();
        
        if (!$record) {
            return [
                Stat::make('Total Filters', '7')
                    ->description('All filters available')
                    ->descriptionIcon('heroicon-m-funnel')
                    ->color('success'),
                    
                Stat::make('Locked Filters', '0')
                    ->description('No filters locked')
                    ->descriptionIcon('heroicon-m-lock-closed')
                    ->color('success'),
                    
                Stat::make('Unlocked Filters', '7')
                    ->description('All filters available to everyone')
                    ->descriptionIcon('heroicon-m-lock-open')
                    ->color('success'),
            ];
        }

        $lockedCount = $record->getLockedFilters()->count();
        $unlockedCount = $record->getUnlockedFilters()->count();
        $totalCount = 7; // Total number of filters

        return [
            Stat::make('Total Filters', $totalCount)
                ->description('Available filter types')
                ->descriptionIcon('heroicon-m-funnel')
                ->color('primary'),
                
            Stat::make('Locked Filters', $lockedCount)
                ->description($lockedCount > 0 ? 'Premium only filters' : 'No locked filters')
                ->descriptionIcon('heroicon-m-lock-closed')
                ->color($lockedCount > 0 ? 'danger' : 'success'),
                
            Stat::make('Unlocked Filters', $unlockedCount)
                ->description($unlockedCount > 0 ? 'Available to all users' : 'All filters locked')
                ->descriptionIcon('heroicon-m-lock-open')
                ->color($unlockedCount > 0 ? 'success' : 'danger'),
                
            Stat::make('Premium Conversion Rate', $this->calculateConversionRate($lockedCount, $totalCount))
                ->description('Percentage of premium filters')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($lockedCount > 3 ? 'warning' : 'info'),
        ];
    }

    private function calculateConversionRate(int $lockedCount, int $totalCount): string
    {
        if ($totalCount === 0) return '0%';
        
        $percentage = round(($lockedCount / $totalCount) * 100);
        return $percentage . '%';
    }

    protected function getColumns(): int
    {
        return 4;
    }
}
