<?php

namespace App\Filament\Pages;

use App\Filament\Actions\IsAdminAction;
use App\Models\FilterAccessControl;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class FilterManagement extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static string $view = 'filament.pages.filter-management';

    protected static ?string $navigationLabel = 'Filter Management';

    protected static ?string $title = 'Filter Access Control Management';

    protected static ?int $navigationSort = 6;

    protected static ?string $navigationGroup = 'App Settings';

    public ?array $data = [];

    public function mount(): void
    {
        $record = FilterAccessControl::firstOrCreate([]);
        $this->form->fill($record->toArray());
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Filter Access Control')
                    ->description('Manage which filters require premium subscription')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Card::make()
                                    ->schema([
                                        Forms\Components\Toggle::make('working_hours_locked')
                                            ->label('Working Hours Filter')
                                            ->helperText('Lock "Open now" filter for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),

                                        Forms\Components\Toggle::make('rating_locked')
                                            ->label('Rating Filter')
                                            ->helperText('Lock rating filters (3.5+, 4+, 4.5+) for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),

                                        Forms\Components\Toggle::make('family_friendly_locked')
                                            ->label('Family Friendly Filter')
                                            ->helperText('Lock family friendly filter for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),

                                        Forms\Components\Toggle::make('budget_locked')
                                            ->label('Budget Filter')
                                            ->helperText('Lock budget filters ($, $$, $$$) for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),
                                    ])
                                    ->columnSpan(1),

                                Forms\Components\Card::make()
                                    ->schema([
                                        Forms\Components\Toggle::make('wheelchair_accessible_locked')
                                            ->label('Wheelchair Accessible Filter')
                                            ->helperText('Lock wheelchair accessible filter for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),

                                        Forms\Components\Toggle::make('pet_friendly_locked')
                                            ->label('Pet Friendly Filter')
                                            ->helperText('Lock pet friendly filter for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),

                                        Forms\Components\Toggle::make('sort_by_distance_locked')
                                            ->label('Sort by Distance')
                                            ->helperText('Lock distance sorting for premium users only')
                                            ->live()
                                            ->afterStateUpdated(fn () => $this->updateRecord()),
                                    ])
                                    ->columnSpan(1),
                            ]),
                    ]),

                Forms\Components\Section::make('Filter Statistics')
                    ->description('Overview of current filter settings')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Placeholder::make('locked_count')
                                    ->label('Locked Filters')
                                    ->content(function () {
                                        $record = FilterAccessControl::first();
                                        return $record ? $record->getLockedFilters()->count() : 0;
                                    }),

                                Forms\Components\Placeholder::make('unlocked_count')
                                    ->label('Unlocked Filters')
                                    ->content(function () {
                                        $record = FilterAccessControl::first();
                                        return $record ? $record->getUnlockedFilters()->count() : 7;
                                    }),

                                Forms\Components\Placeholder::make('premium_percentage')
                                    ->label('Premium Percentage')
                                    ->content(function () {
                                        $record = FilterAccessControl::first();
                                        if (!$record) return '0%';
                                        $locked = $record->getLockedFilters()->count();
                                        return round(($locked / 7) * 100) . '%';
                                    }),
                            ]),
                    ])
                    ->collapsible(),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('lockAll')
                ->label('Lock All Filters')
                ->icon('heroicon-o-lock-closed')
                ->color('danger')
                ->requiresConfirmation()
                ->modalDescription('This will lock all filters for premium users only.')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => true,
                        'rating_locked' => true,
                        'family_friendly_locked' => true,
                        'budget_locked' => true,
                        'wheelchair_accessible_locked' => true,
                        'pet_friendly_locked' => true,
                        'sort_by_distance_locked' => true,
                    ]);

                    $this->form->fill($record->toArray());

                    Notification::make()
                        ->success()
                        ->title('All Filters Locked')
                        ->body('All filters are now locked for premium users only.')
                        ->send();
                }),

            Action::make('unlockAll')
                ->label('Unlock All Filters')
                ->icon('heroicon-o-lock-open')
                ->color('success')
                ->requiresConfirmation()
                ->modalDescription('This will unlock all filters for all users.')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => false,
                        'rating_locked' => false,
                        'family_friendly_locked' => false,
                        'budget_locked' => false,
                        'wheelchair_accessible_locked' => false,
                        'pet_friendly_locked' => false,
                        'sort_by_distance_locked' => false,
                    ]);

                    $this->form->fill($record->toArray());

                    Notification::make()
                        ->success()
                        ->title('All Filters Unlocked')
                        ->body('All filters are now available for all users.')
                        ->send();
                }),

            Action::make('resetDefaults')
                ->label('Reset to Defaults')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->requiresConfirmation()
                ->modalDescription('This will reset all filter settings to their default values (all unlocked).')
                ->action(function () {
                    $record = FilterAccessControl::firstOrCreate([]);
                    $record->update([
                        'working_hours_locked' => false,
                        'rating_locked' => false,
                        'family_friendly_locked' => false,
                        'budget_locked' => false,
                        'wheelchair_accessible_locked' => false,
                        'pet_friendly_locked' => false,
                        'sort_by_distance_locked' => false,
                    ]);

                    $this->form->fill($record->toArray());

                    Notification::make()
                        ->success()
                        ->title('Settings Reset')
                        ->body('All filter settings have been reset to defaults.')
                        ->send();
                }),
        ];
    }

    public function updateRecord(): void
    {
        $data = $this->form->getState();
        $record = FilterAccessControl::firstOrCreate([]);
        $record->update($data);

        Notification::make()
            ->success()
            ->title('Filter Settings Updated')
            ->body('Filter access control settings have been updated.')
            ->send();
    }

    public static function canAccess(): bool
    {
        return IsAdminAction::handle();
    }
}
