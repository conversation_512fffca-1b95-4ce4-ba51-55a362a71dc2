<?php

namespace App\Filament\Resources\FilterAccessControlResource\Pages;

use App\Filament\Resources\FilterAccessControlResource;
use Filament\Resources\Pages\CreateRecord;

class CreateFilterAccessControl extends CreateRecord
{
    protected static string $resource = FilterAccessControlResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set default values if not provided
        return array_merge([
            'working_hours_locked' => false,
            'rating_locked' => false,
            'family_friendly_locked' => false,
            'budget_locked' => false,
            'wheelchair_accessible_locked' => false,
            'pet_friendly_locked' => false,
            'sort_by_distance_locked' => false,
        ], $data);
    }
}
